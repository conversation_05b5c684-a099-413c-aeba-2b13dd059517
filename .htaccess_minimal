# Minimal .htaccess for cPanel Django hosting
# This avoids complex rewrite rules that cause download issues

# Basic Passenger configuration
PassengerEnabled on
PassengerAppRoot /home/<USER>/public_html/KodeSQL
PassengerPython /home/<USER>/virtualenv/public_html/KodeSQL/3.10/bin/python

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
</IfModule>

# Protect sensitive files
<Files "*.py">
    <RequireAll>
        Require all denied
        Require local
    </RequireAll>
</Files>

<Files "passenger_wsgi.py">
    Require all granted
</Files>

<Files ".env">
    Require all denied
</Files>

<Files "*.log">
    Require all denied
</Files>

# Let Django handle all URL routing
# No complex rewrite rules - just basic protection
