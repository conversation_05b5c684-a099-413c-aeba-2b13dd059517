# Generated by Django 5.2.1 on 2025-07-06 09:34

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("challenges", "0003_alter_challengetable_table_name"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="XPTransaction",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "transaction_type",
                    models.CharField(
                        choices=[
                            ("challenge_completion", "Challenge Completion"),
                            ("bonus", "Bonus XP"),
                            ("adjustment", "Manual Adjustment"),
                        ],
                        default="challenge_completion",
                        max_length=20,
                    ),
                ),
                (
                    "xp_amount",
                    models.IntegerField(
                        help_text="XP amount (can be negative for deductions)"
                    ),
                ),
                (
                    "description",
                    models.CharField(
                        help_text="Description of the XP transaction", max_length=255
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "challenge",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="xp_transactions",
                        to="challenges.challenge",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="xp_transactions",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
    ]
