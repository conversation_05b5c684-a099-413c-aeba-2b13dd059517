#!/usr/bin/env python3
"""
Simple URL routing test without database dependencies
"""

import os
import sys

def test_url_imports():
    """Test if URL patterns can be imported"""
    print("🔍 Testing URL Pattern Imports...")
    
    try:
        # Add current directory to Python path
        sys.path.insert(0, os.getcwd())
        
        # Test importing URL patterns
        from sqlplayground.urls import urlpatterns
        print(f"✅ Main URL patterns imported successfully ({len(urlpatterns)} patterns)")
        
        # Test importing app URLs
        apps_to_test = ['core', 'users', 'challenges', 'tutorials', 'courses', 'editor']
        
        for app in apps_to_test:
            try:
                app_urls = __import__(f'{app}.urls', fromlist=['urlpatterns'])
                patterns = getattr(app_urls, 'urlpatterns', [])
                print(f"✅ {app}.urls imported successfully ({len(patterns)} patterns)")
            except ImportError as e:
                print(f"❌ {app}.urls import failed: {e}")
            except Exception as e:
                print(f"⚠️  {app}.urls warning: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ URL import test failed: {e}")
        return False

def test_settings_import():
    """Test if settings can be imported"""
    print("\n⚙️  Testing Settings Import...")
    
    try:
        from sqlplayground.settings import DEBUG, ALLOWED_HOSTS, STATIC_URL, MEDIA_URL
        
        print(f"✅ Settings imported successfully")
        print(f"   DEBUG: {DEBUG}")
        print(f"   ALLOWED_HOSTS: {ALLOWED_HOSTS}")
        print(f"   STATIC_URL: {STATIC_URL}")
        print(f"   MEDIA_URL: {MEDIA_URL}")
        
        return True
        
    except Exception as e:
        print(f"❌ Settings import failed: {e}")
        return False

def check_file_structure():
    """Check if required files exist"""
    print("\n📁 Checking File Structure...")
    
    required_files = [
        'manage.py',
        'passenger_wsgi.py',
        '.htaccess',
        'sqlplayground/settings.py',
        'sqlplayground/urls.py',
        'core/urls.py',
        'users/urls.py',
    ]
    
    all_exist = True
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - Missing!")
            all_exist = False
    
    return all_exist

def check_htaccess_content():
    """Check .htaccess content"""
    print("\n🔧 Checking .htaccess Configuration...")
    
    try:
        with open('.htaccess', 'r') as f:
            content = f.read()
        
        required_configs = [
            ('PassengerEnabled on', 'Passenger enabled'),
            ('PassengerAppRoot', 'App root configured'),
            ('PassengerPython', 'Python path configured'),
            ('RewriteEngine On', 'URL rewriting enabled'),
            ('RewriteRule', 'Rewrite rules present'),
            ('passenger_wsgi.py', 'WSGI file referenced'),
        ]
        
        for config, description in required_configs:
            if config in content:
                print(f"✅ {description}")
            else:
                print(f"❌ Missing: {description}")
        
        return True
        
    except Exception as e:
        print(f"❌ .htaccess check failed: {e}")
        return False

def main():
    """Main function"""
    print("🚀 Simple Django Configuration Test")
    print("=" * 40)
    
    tests = [
        ("File Structure", check_file_structure),
        (".htaccess Configuration", check_htaccess_content),
        ("Settings Import", test_settings_import),
        ("URL Pattern Imports", test_url_imports),
    ]
    
    results = []
    for name, test_func in tests:
        print(f"\n--- {name} ---")
        try:
            result = test_func()
            results.append((name, result))
        except Exception as e:
            print(f"❌ {name} failed: {e}")
            results.append((name, False))
    
    # Summary
    print("\n" + "=" * 40)
    print("📋 TEST RESULTS:")
    
    all_passed = True
    for name, result in results:
        status = "✅" if result else "❌"
        print(f"{status} {name}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 Configuration looks good!")
        print("\n🔧 Deployment Steps:")
        print("1. Upload files to cPanel")
        print("2. Restart Python application")
        print("3. Test URLs on live site")
    else:
        print("\n⚠️  Please fix the issues above before deploying.")
    
    print("\n🌐 Test these URLs after deployment:")
    print("   https://kodesql.in/")
    print("   https://kodesql.in/dashboard/")
    print("   https://kodesql.in/auth/login/")
    print("   https://kodesql.in/debug-routing/")

if __name__ == "__main__":
    main()
