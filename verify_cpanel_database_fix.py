#!/usr/bin/env python3
"""
cPanel Database Fix Verification Script
Run this script on your cPanel server to verify the database connection fixes.
"""

import os
import sys

def test_package_imports():
    """Test if required packages are available"""
    print("🔍 Testing Package Imports...")
    
    # Test PyMySQL
    try:
        import pymysql
        print("✅ PyMySQL is available")
        pymysql_version = getattr(pymysql, '__version__', 'Unknown')
        print(f"   Version: {pymysql_version}")
    except ImportError:
        print("❌ PyMySQL is NOT available - Install with: pip install PyMySQL")
        return False
    
    # Test psycopg2
    try:
        import psycopg2
        print("✅ psycopg2 is available")
        psycopg2_version = getattr(psycopg2, '__version__', 'Unknown')
        print(f"   Version: {psycopg2_version}")
    except ImportError:
        print("❌ psycopg2 is NOT available - Install with: pip install psycopg2")
        return False
    
    return True

def test_environment_variables():
    """Test if environment variables are set correctly"""
    print("\n🔍 Testing Environment Variables...")
    
    required_vars = [
        'QUERY_MYSQL_HOST',
        'QUERY_MYSQL_USER', 
        'QUERY_MYSQL_PASSWORD',
        'QUERY_MYSQL_DB_NAME',
        'QUERY_POSTGRES_HOST',
        'QUERY_POSTGRES_USER',
        'QUERY_POSTGRES_PASSWORD', 
        'QUERY_POSTGRES_DB_NAME'
    ]
    
    missing_vars = []
    for var in required_vars:
        value = os.environ.get(var)
        if value:
            # Don't print passwords
            if 'PASSWORD' in var:
                print(f"✅ {var}: [SET]")
            else:
                print(f"✅ {var}: {value}")
        else:
            print(f"❌ {var}: NOT SET")
            missing_vars.append(var)
    
    return len(missing_vars) == 0

def test_mysql_connection():
    """Test MySQL connection using PyMySQL"""
    print("\n🔍 Testing MySQL Connection...")
    
    try:
        import pymysql
        
        config = {
            'host': os.environ.get('QUERY_MYSQL_HOST', 'localhost'),
            'port': int(os.environ.get('QUERY_MYSQL_PORT', '3306')),
            'user': os.environ.get('QUERY_MYSQL_USER', 'kodesqli_mysql'),
            'password': os.environ.get('QUERY_MYSQL_PASSWORD', 'forgex99'),
            'charset': 'utf8mb4'
        }
        
        print(f"   Host: {config['host']}")
        print(f"   Port: {config['port']}")
        print(f"   User: {config['user']}")
        
        # Test server connection
        conn = pymysql.connect(
            host=config['host'],
            port=config['port'],
            user=config['user'],
            password=config['password'],
            charset=config['charset']
        )
        
        cursor = conn.cursor()
        cursor.execute("SELECT VERSION()")
        version = cursor.fetchone()
        print(f"✅ MySQL Server Connection Successful!")
        print(f"   Version: {version[0]}")
        
        # Test database access
        db_name = os.environ.get('QUERY_MYSQL_DB_NAME', 'kodesqli_queries_mysql')
        try:
            cursor.execute(f"USE `{db_name}`")
            print(f"✅ MySQL Database '{db_name}' Access Successful!")
        except Exception as e:
            print(f"⚠️  MySQL Database '{db_name}' Access Failed: {e}")
            print("   Make sure the database exists and user has permissions")
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ MySQL Connection Failed: {e}")
        return False

def test_postgresql_connection():
    """Test PostgreSQL connection using psycopg2"""
    print("\n🔍 Testing PostgreSQL Connection...")
    
    try:
        import psycopg2
        
        config = {
            'host': os.environ.get('QUERY_POSTGRES_HOST', 'localhost'),
            'port': int(os.environ.get('QUERY_POSTGRES_PORT', '5432')),
            'user': os.environ.get('QUERY_POSTGRES_USER', 'kodesqli_postgres'),
            'password': os.environ.get('QUERY_POSTGRES_PASSWORD', 'forgex99'),
        }
        
        print(f"   Host: {config['host']}")
        print(f"   Port: {config['port']}")
        print(f"   User: {config['user']}")
        
        # Test database connection
        db_name = os.environ.get('QUERY_POSTGRES_DB_NAME', 'kodesqli_queries_pg')
        config['database'] = db_name
        
        conn = psycopg2.connect(**config)
        cursor = conn.cursor()
        cursor.execute("SELECT version()")
        version = cursor.fetchone()
        print(f"✅ PostgreSQL Connection Successful!")
        print(f"   Version: {version[0]}")
        print(f"✅ PostgreSQL Database '{db_name}' Access Successful!")
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ PostgreSQL Connection Failed: {e}")
        return False

def test_django_setup():
    """Test if Django can be set up with the new configuration"""
    print("\n🔍 Testing Django Setup...")
    
    try:
        # Add current directory to Python path
        current_dir = os.path.dirname(os.path.abspath(__file__))
        if current_dir not in sys.path:
            sys.path.insert(0, current_dir)
        
        # Set Django settings
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'sqlplayground.settings')
        
        import django
        django.setup()
        
        print("✅ Django setup successful!")
        
        # Test database connections through Django
        from django.db import connections
        
        # Test default database
        try:
            default_conn = connections['default']
            with default_conn.cursor() as cursor:
                cursor.execute("SELECT 1")
            print("✅ Django default database connection successful!")
        except Exception as e:
            print(f"⚠️  Django default database connection failed: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Django setup failed: {e}")
        return False

def main():
    """Main verification function"""
    print("🚀 cPanel Database Fix Verification")
    print("=" * 50)
    
    # Load environment variables from .env if available
    try:
        from dotenv import load_dotenv
        load_dotenv()
        print("✅ Loaded environment variables from .env file")
    except ImportError:
        print("⚠️  python-dotenv not available, using system environment variables")
    except Exception as e:
        print(f"⚠️  Could not load .env file: {e}")
    
    # Run tests
    tests_passed = 0
    total_tests = 5
    
    if test_package_imports():
        tests_passed += 1
    
    if test_environment_variables():
        tests_passed += 1
    
    if test_mysql_connection():
        tests_passed += 1
    
    if test_postgresql_connection():
        tests_passed += 1
    
    if test_django_setup():
        tests_passed += 1
    
    # Summary
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! Database connections should work on cPanel.")
        print("\nNext steps:")
        print("1. Upload the modified files to your cPanel")
        print("2. Test the challenge solve page")
        print("3. Try running queries with both MySQL and PostgreSQL engines")
    else:
        print("⚠️  Some tests failed. Please fix the issues before deploying.")
        print("\nCommon fixes:")
        print("- Install missing packages: pip install PyMySQL psycopg2")
        print("- Check database credentials in .env file")
        print("- Verify databases exist in cPanel")
        print("- Ensure database users have proper permissions")

if __name__ == "__main__":
    main()
