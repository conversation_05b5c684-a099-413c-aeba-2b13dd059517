#!/usr/bin/env python3
"""
Final Database Fix Test
Test the complete database fix with correct credentials
"""

import os
import sys

def test_mysql_with_correct_credentials():
    """Test MySQL with the discovered correct credentials"""
    print("🔍 Testing MySQL with Correct Credentials...")
    
    try:
        import pymysql
        
        # Use the correct credentials discovered
        conn = pymysql.connect(
            host='127.0.0.1',
            port=3306,
            user='kodesqli_kodesql_mysql_user',
            password='forgex99',
            database='kodesqli_kodesql_queries_mysql',
            charset='utf8mb4'
        )
        
        cursor = conn.cursor(pymysql.cursors.DictCursor)
        
        # Test the table prefix approach (what the challenge system will do)
        test_prefix = "challenge_test_12345"
        
        # Create a test table with prefix
        cursor.execute(f"""
            CREATE TABLE IF NOT EXISTS {test_prefix}_employees (
                id INT PRIMARY KEY,
                name VARCHA<PERSON>(100),
                department VARCHAR(50)
            )
        """)
        print("✅ Table creation with prefix successful")
        
        # Insert test data
        cursor.execute(f"""
            INSERT INTO {test_prefix}_employees (id, name, department) 
            VALUES (1, '<PERSON>', 'Engineering')
        """)
        print("✅ Insert with prefix successful")
        
        # Query test data
        cursor.execute(f"SELECT * FROM {test_prefix}_employees")
        results = cursor.fetchall()
        print(f"✅ Query with prefix successful: {results}")
        
        # Cleanup
        cursor.execute(f"DROP TABLE IF EXISTS {test_prefix}_employees")
        print("✅ Cleanup successful")
        
        cursor.close()
        conn.close()
        
        print("🎉 MySQL test with table prefixes PASSED!")
        return True
        
    except Exception as e:
        print(f"❌ MySQL test failed: {e}")
        return False

def test_django_mysql_connection():
    """Test Django MySQL connection"""
    print("\n🔍 Testing Django MySQL Connection...")
    
    try:
        # Setup Django
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'sqlplayground.settings')
        import django
        django.setup()
        
        from django.db import connections
        
        # Test MySQL connection through Django
        mysql_conn = connections['query_mysql']
        with mysql_conn.cursor() as cursor:
            cursor.execute("SELECT 1 as test")
            result = cursor.fetchone()
            print(f"✅ Django MySQL connection successful: {result}")
            return True
            
    except Exception as e:
        print(f"❌ Django MySQL connection failed: {e}")
        return False

def test_challenge_execution_simulation():
    """Simulate the challenge execution process"""
    print("\n🔍 Simulating Challenge Execution Process...")
    
    try:
        from challenges.utils import _execute_mysql_dual_dataset, _add_table_prefix_to_sql
        
        # Simulate challenge data
        db_name = "challenge_1_temp_abc123"
        
        schema_sql = """
        CREATE TABLE employees (
            id INT PRIMARY KEY,
            name VARCHAR(100),
            department VARCHAR(50)
        );
        """
        
        dataset_sql = """
        INSERT INTO employees VALUES (1, 'John', 'Engineering');
        INSERT INTO employees VALUES (2, 'Jane', 'Marketing');
        """
        
        query = "SELECT * FROM employees WHERE department = 'Engineering';"
        
        # Test the actual function that will be used
        result = _execute_mysql_dual_dataset(db_name, schema_sql, dataset_sql, query)
        
        if result['success']:
            print("✅ Challenge execution simulation successful!")
            print(f"   Results: {result.get('results', [])}")
            print(f"   Row count: {result.get('row_count', 0)}")
            return True
        else:
            print(f"❌ Challenge execution simulation failed: {result.get('error')}")
            return False
            
    except Exception as e:
        print(f"❌ Challenge execution simulation failed: {e}")
        return False

def main():
    print("🚀 Final Database Fix Test")
    print("="*60)
    
    # Load environment variables
    try:
        from dotenv import load_dotenv
        load_dotenv()
        print("✅ Environment variables loaded")
    except:
        print("⚠️  Could not load .env file")
    
    # Run tests
    mysql_basic = test_mysql_with_correct_credentials()
    django_mysql = test_django_mysql_connection()
    challenge_sim = test_challenge_execution_simulation()
    
    print("\n" + "="*60)
    print("📊 TEST RESULTS")
    print("="*60)
    
    tests = [
        ("MySQL Basic Connection", mysql_basic),
        ("Django MySQL Connection", django_mysql), 
        ("Challenge Execution Simulation", challenge_sim)
    ]
    
    passed = sum(1 for _, result in tests if result)
    total = len(tests)
    
    for test_name, result in tests:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED!")
        print("The database connection issues are fixed.")
        print("You can now test the challenge solve page.")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed.")
        print("Check the error messages above for details.")

if __name__ == "__main__":
    main()
