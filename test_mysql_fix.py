#!/usr/bin/env python3
"""
Quick test for MySQL connection fix
Run this to verify the MySQL access issue is resolved
"""

import os
import sys

# Add project directory to path
project_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_dir)

def test_mysql_connection():
    """Test MySQL connection with the updated credentials"""
    print("🔍 Testing MySQL Connection with Updated Credentials...")
    
    try:
        import pymysql
        
        # Use the updated credentials from .env
        config = {
            'host': 'localhost',
            'port': 3306,
            'user': 'kodesqli_kodesql_mysql_user',  # Updated username
            'password': 'forgex99',
            'database': 'kodesqli_queries_mysql',
            'charset': 'utf8mb4'
        }
        
        print(f"Connecting with user: {config['user']}")
        print(f"Database: {config['database']}")
        
        conn = pymysql.connect(**config)
        cursor = conn.cursor()
        
        # Test basic query
        cursor.execute("SELECT 1 as test")
        result = cursor.fetchone()
        print(f"✅ Basic query successful: {result}")
        
        # Test table creation (what was failing before)
        test_table = "test_temp_table_12345"
        cursor.execute(f"CREATE TABLE IF NOT EXISTS {test_table} (id INT, name VARCHAR(50))")
        print(f"✅ Table creation successful: {test_table}")
        
        # Test insert
        cursor.execute(f"INSERT INTO {test_table} VALUES (1, 'test')")
        print("✅ Insert successful")
        
        # Test select
        cursor.execute(f"SELECT * FROM {test_table}")
        results = cursor.fetchall()
        print(f"✅ Select successful: {results}")
        
        # Cleanup
        cursor.execute(f"DROP TABLE IF EXISTS {test_table}")
        print("✅ Cleanup successful")
        
        cursor.close()
        conn.close()
        
        print("\n🎉 All MySQL tests passed! The access issue should be fixed.")
        return True
        
    except Exception as e:
        print(f"❌ MySQL test failed: {e}")
        return False

def test_challenge_execution():
    """Test the challenge execution with the new table prefix approach"""
    print("\n🔍 Testing Challenge Execution Logic...")
    
    try:
        # Import the updated function
        from challenges.utils import _add_table_prefix_to_sql
        
        # Test SQL transformation
        original_sql = """
        CREATE TABLE employees (id INT, name VARCHAR(100));
        INSERT INTO employees VALUES (1, 'John');
        SELECT * FROM employees;
        """
        
        prefix = "challenge_1_temp_abc123"
        transformed_sql = _add_table_prefix_to_sql(original_sql, prefix)
        
        print("Original SQL:")
        print(original_sql)
        print("\nTransformed SQL:")
        print(transformed_sql)
        
        # Check if transformation worked
        if f"{prefix}_employees" in transformed_sql:
            print("✅ SQL transformation successful")
            return True
        else:
            print("❌ SQL transformation failed")
            return False
            
    except Exception as e:
        print(f"❌ Challenge execution test failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Testing MySQL Access Fix")
    print("=" * 50)
    
    # Load environment variables
    try:
        from dotenv import load_dotenv
        load_dotenv()
        print("✅ Environment variables loaded")
    except:
        print("⚠️  Could not load .env file")
    
    # Run tests
    mysql_ok = test_mysql_connection()
    challenge_ok = test_challenge_execution()
    
    print("\n" + "=" * 50)
    if mysql_ok and challenge_ok:
        print("🎉 All tests passed! You can now try the challenge solve page.")
        print("\nNext steps:")
        print("1. Go to any challenge page")
        print("2. Try running a simple query like: SELECT 1")
        print("3. The MySQL access error should be resolved")
    else:
        print("⚠️  Some tests failed. Check the error messages above.")
