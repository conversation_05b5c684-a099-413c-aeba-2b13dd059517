#!/usr/bin/env python3
"""
Fix 404 errors on cPanel - Landing page loads but other URLs give 404
This script diagnoses and fixes URL routing issues
"""

import os
import sys
import django

def setup_django():
    """Setup Django environment"""
    sys.path.insert(0, os.getcwd())
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'sqlplayground.settings')
    django.setup()

def test_url_patterns():
    """Test Django URL patterns"""
    print("🔧 Testing Django URL Patterns...")
    
    try:
        from django.urls import reverse
        from django.conf import settings
        
        # Test core URLs
        test_urls = [
            ('core:landing_page', '/'),
            ('core:dashboard', '/dashboard/'),
            ('core:about', '/about/'),
            ('users:login', '/auth/login/'),
            ('users:register', '/auth/register/'),
            ('admin:index', '/admin/'),
        ]
        
        print(f"DEBUG mode: {settings.DEBUG}")
        print(f"ROOT_URLCONF: {settings.ROOT_URLCONF}")
        
        for name, expected_path in test_urls:
            try:
                url = reverse(name)
                status = "✅" if url == expected_path else f"⚠️  Expected {expected_path}, got {url}"
                print(f"{status} {name}: {url}")
            except Exception as e:
                print(f"❌ {name}: {e}")
        
        return True
    except Exception as e:
        print(f"❌ URL patterns test failed: {e}")
        return False

def check_wsgi_configuration():
    """Check WSGI configuration"""
    print("\n🔧 Checking WSGI Configuration...")
    
    try:
        # Test WSGI application
        from django.core.wsgi import get_wsgi_application
        application = get_wsgi_application()
        print("✅ WSGI application created successfully")
        
        # Check if passenger_wsgi.py exists and is executable
        if os.path.exists('passenger_wsgi.py'):
            stat = os.stat('passenger_wsgi.py')
            permissions = oct(stat.st_mode)[-3:]
            print(f"✅ passenger_wsgi.py exists with permissions: {permissions}")
            
            if permissions != '755':
                print("⚠️  Setting executable permissions on passenger_wsgi.py")
                os.chmod('passenger_wsgi.py', 0o755)
        else:
            print("❌ passenger_wsgi.py not found")
            return False
        
        return True
    except Exception as e:
        print(f"❌ WSGI configuration check failed: {e}")
        return False

def check_static_files():
    """Check static files configuration"""
    print("\n🔧 Checking Static Files...")
    
    try:
        from django.conf import settings
        from django.contrib.staticfiles import finders
        
        print(f"STATIC_URL: {settings.STATIC_URL}")
        print(f"STATIC_ROOT: {settings.STATIC_ROOT}")
        print(f"DEBUG: {settings.DEBUG}")
        
        # Check if static files exist
        if os.path.exists(str(settings.STATIC_ROOT)):
            print("✅ STATIC_ROOT directory exists")
        else:
            print("⚠️  STATIC_ROOT directory doesn't exist")
            print("Run: python manage.py collectstatic --noinput")
        
        # Test finding a static file
        css_file = finders.find('css/style.css')
        if css_file:
            print(f"✅ Found static file: {css_file}")
        else:
            print("⚠️  Static files may not be collected")
        
        return True
    except Exception as e:
        print(f"❌ Static files check failed: {e}")
        return False

def create_debug_urls():
    """Create debug URLs to test routing"""
    print("\n🔧 Creating Debug URLs...")
    
    debug_urls = '''
from django.http import HttpResponse
from django.urls import path

def debug_view(request):
    return HttpResponse(f"""
    <h1>🐛 Debug Info</h1>
    <p><strong>Path:</strong> {request.path}</p>
    <p><strong>Method:</strong> {request.method}</p>
    <p><strong>User:</strong> {request.user}</p>
    <p><strong>GET params:</strong> {dict(request.GET)}</p>
    <p><strong>Headers:</strong> {dict(request.META)}</p>
    <hr>
    <p>If you see this, Django URL routing is working!</p>
    <p><a href="/">← Back to Home</a></p>
    """)

# Add this to your main urls.py for testing
debug_urlpatterns = [
    path('debug-routing/', debug_view, name='debug_routing'),
]
'''
    
    try:
        with open('debug_urls.py', 'w') as f:
            f.write(debug_urls)
        print("✅ Created debug_urls.py")
        print("💡 Add this to your main urls.py:")
        print("   from debug_urls import debug_urlpatterns")
        print("   urlpatterns += debug_urlpatterns")
        print("   Then test: https://kodesql.in/debug-routing/")
        return True
    except Exception as e:
        print(f"❌ Failed to create debug URLs: {e}")
        return False

def check_htaccess():
    """Check .htaccess configuration"""
    print("\n🔧 Checking .htaccess Configuration...")
    
    if os.path.exists('.htaccess'):
        with open('.htaccess', 'r') as f:
            content = f.read()
        
        # Check for key configurations
        checks = [
            ('PassengerAppRoot', 'Passenger app root configured'),
            ('PassengerPython', 'Python path configured'),
            ('RewriteEngine On', 'URL rewriting enabled'),
            ('RewriteRule', 'Rewrite rules present'),
        ]
        
        for check, description in checks:
            if check in content:
                print(f"✅ {description}")
            else:
                print(f"❌ Missing: {description}")
        
        print("\n📋 Current .htaccess content:")
        print("-" * 40)
        print(content[:500] + "..." if len(content) > 500 else content)
        print("-" * 40)
        
        return True
    else:
        print("❌ .htaccess file not found")
        return False

def create_alternative_htaccess():
    """Create alternative .htaccess configurations"""
    print("\n🔧 Creating Alternative .htaccess...")
    
    # Simple .htaccess for testing
    simple_htaccess = '''# Simple .htaccess for testing
PassengerEnabled on
PassengerAppRoot /home/<USER>/public_html/KodeSQL
PassengerPython /home/<USER>/virtualenv/public_html/KodeSQL/3.10/bin/python

# Route everything through Django
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ / [QSA,L]
'''
    
    try:
        with open('.htaccess_simple', 'w') as f:
            f.write(simple_htaccess)
        print("✅ Created .htaccess_simple")
        print("💡 To test: mv .htaccess .htaccess_backup && mv .htaccess_simple .htaccess")
        return True
    except Exception as e:
        print(f"❌ Failed to create alternative .htaccess: {e}")
        return False

def main():
    """Main function to fix 404 errors"""
    print("🚀 Fixing cPanel 404 Errors")
    print("=" * 40)
    
    # Setup Django
    try:
        setup_django()
        print("✅ Django setup successful")
    except Exception as e:
        print(f"❌ Django setup failed: {e}")
        return
    
    # Run diagnostics
    diagnostics = [
        ("URL Patterns", test_url_patterns),
        ("WSGI Configuration", check_wsgi_configuration),
        ("Static Files", check_static_files),
        (".htaccess", check_htaccess),
        ("Create Debug URLs", create_debug_urls),
        ("Alternative .htaccess", create_alternative_htaccess),
    ]
    
    results = []
    for name, diagnostic_func in diagnostics:
        print(f"\n--- {name} ---")
        try:
            result = diagnostic_func()
            results.append((name, result))
        except Exception as e:
            print(f"❌ {name} failed: {e}")
            results.append((name, False))
    
    # Summary
    print("\n" + "=" * 40)
    print("📋 DIAGNOSTIC RESULTS:")
    
    for name, result in results:
        status = "✅" if result else "❌"
        print(f"{status} {name}")
    
    print("\n🔧 RECOMMENDED FIXES:")
    print("1. Try the optimized .htaccess:")
    print("   mv .htaccess .htaccess_backup")
    print("   mv .htaccess_cpanel_optimized .htaccess")
    print("   Restart Python app")
    
    print("\n2. If still not working, try simple .htaccess:")
    print("   mv .htaccess_simple .htaccess")
    print("   Restart Python app")
    
    print("\n3. Test debug URL:")
    print("   https://kodesql.in/debug-routing/")
    
    print("\n4. Check cPanel error logs for specific errors")
    
    print("\n5. Ensure Python app is properly configured in cPanel")

if __name__ == "__main__":
    main()
