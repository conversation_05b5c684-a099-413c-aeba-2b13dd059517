#!/usr/bin/env python3
"""
Fix cPanel database migration issues
This script specifically fixes the missing users_userdatabase table
"""

import os
import sys

def setup_django():
    """Setup Django environment"""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'sqlplayground.settings')
    
    try:
        import django
        django.setup()
        return True
    except Exception as e:
        print(f"❌ Django setup failed: {e}")
        return False

def check_database_connection():
    """Check if database connection works"""
    print("🔍 Checking database connection...")
    
    try:
        from django.db import connection
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            if result:
                print("✅ Database connection successful")
                return True
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

def check_migration_status():
    """Check which migrations have been applied"""
    print("\n📋 Checking migration status...")
    
    try:
        from django.db import connection
        
        with connection.cursor() as cursor:
            # Check if django_migrations table exists
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = 'django_migrations'
                )
            """)
            
            if not cursor.fetchone()[0]:
                print("❌ django_migrations table doesn't exist")
                return False
            
            # Check users app migrations
            cursor.execute("""
                SELECT name FROM django_migrations 
                WHERE app = 'users' 
                ORDER BY name
            """)
            
            migrations = [row[0] for row in cursor.fetchall()]
            print(f"✅ Applied users migrations: {len(migrations)}")
            
            for migration in migrations:
                print(f"   - {migration}")
            
            # Check if UserDatabase migration is applied
            if '0008_add_userdatabase' in migrations or '0001_initial' in migrations:
                print("✅ UserDatabase migration should be applied")
            else:
                print("❌ UserDatabase migration not found")
            
            return True
            
    except Exception as e:
        print(f"❌ Migration status check failed: {e}")
        return False

def check_table_exists():
    """Check if users_userdatabase table exists"""
    print("\n🔍 Checking if users_userdatabase table exists...")
    
    try:
        from django.db import connection
        
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = 'users_userdatabase'
                )
            """)
            
            exists = cursor.fetchone()[0]
            if exists:
                print("✅ users_userdatabase table exists")
                
                # Check table structure
                cursor.execute("""
                    SELECT column_name, data_type 
                    FROM information_schema.columns 
                    WHERE table_name = 'users_userdatabase'
                    ORDER BY ordinal_position
                """)
                
                columns = cursor.fetchall()
                print(f"   Table has {len(columns)} columns:")
                for col_name, col_type in columns:
                    print(f"   - {col_name}: {col_type}")
                
                return True
            else:
                print("❌ users_userdatabase table does not exist")
                return False
                
    except Exception as e:
        print(f"❌ Table check failed: {e}")
        return False

def create_userdatabase_table():
    """Create the UserDatabase table manually"""
    print("\n🔧 Creating users_userdatabase table...")
    
    try:
        from django.db import connection
        
        # SQL to create the UserDatabase table
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS users_userdatabase (
            id BIGSERIAL PRIMARY KEY,
            database_path VARCHAR(255) NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
            last_accessed TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
            user_id BIGINT NOT NULL UNIQUE REFERENCES users_user(id) ON DELETE CASCADE
        );
        """
        
        with connection.cursor() as cursor:
            cursor.execute(create_table_sql)
            print("✅ users_userdatabase table created")
            
            # Create index for better performance
            cursor.execute("""
                CREATE INDEX IF NOT EXISTS users_userdatabase_user_id_idx 
                ON users_userdatabase(user_id);
            """)
            print("✅ Index created")
            
            return True
            
    except Exception as e:
        print(f"❌ Table creation failed: {e}")
        return False

def update_migration_state():
    """Update Django migration state"""
    print("\n📝 Updating migration state...")
    
    try:
        from django.db import connection
        
        with connection.cursor() as cursor:
            # Check if migration record exists
            cursor.execute("""
                SELECT COUNT(*) FROM django_migrations 
                WHERE app = 'users' AND name = '0008_add_userdatabase'
            """)
            
            count = cursor.fetchone()[0]
            
            if count == 0:
                # Add migration record
                cursor.execute("""
                    INSERT INTO django_migrations (app, name, applied) 
                    VALUES ('users', '0008_add_userdatabase', NOW())
                """)
                print("✅ Migration state updated")
            else:
                print("✅ Migration already recorded")
            
            return True
            
    except Exception as e:
        print(f"❌ Migration state update failed: {e}")
        return False

def test_userdatabase_model():
    """Test if UserDatabase model works"""
    print("\n🧪 Testing UserDatabase model...")
    
    try:
        from users.models import UserDatabase, User
        
        # Test model import
        print("✅ UserDatabase model imported")
        
        # Test querying
        count = UserDatabase.objects.count()
        print(f"✅ UserDatabase query works - {count} records")
        
        # Test if we can create a UserDatabase for an existing user
        user = User.objects.first()
        if user:
            db_obj, created = UserDatabase.objects.get_or_create(
                user=user,
                defaults={'database_path': f'user_{user.id}.db'}
            )
            if created:
                print(f"✅ Created UserDatabase for user: {user.email}")
            else:
                print(f"✅ UserDatabase already exists for user: {user.email}")
        else:
            print("⚠️  No users found to test with")
        
        return True
        
    except Exception as e:
        print(f"❌ UserDatabase model test failed: {e}")
        return False

def run_migrations():
    """Run Django migrations"""
    print("\n🔄 Running Django migrations...")
    
    try:
        from django.core.management import execute_from_command_line
        
        # Run migrations for users app
        execute_from_command_line(['manage.py', 'migrate', 'users', '--verbosity=1'])
        print("✅ Users migrations completed")
        
        # Run all migrations
        execute_from_command_line(['manage.py', 'migrate', '--verbosity=1'])
        print("✅ All migrations completed")
        
        return True
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        return False

def main():
    """Main function to fix database issues"""
    print("🚀 Fixing cPanel Database Migration Issues")
    print("=" * 50)
    
    # Setup Django
    if not setup_django():
        return
    
    # Check database connection
    if not check_database_connection():
        print("\n❌ Cannot proceed without database connection")
        return
    
    # Check migration status
    check_migration_status()
    
    # Check if table exists
    table_exists = check_table_exists()
    
    if not table_exists:
        print("\n🔧 Table missing - creating it manually...")
        
        # Create table manually
        if create_userdatabase_table():
            # Update migration state
            update_migration_state()
            
            # Test the model
            if test_userdatabase_model():
                print("\n🎉 SUCCESS! UserDatabase table is now working")
                print("\n📋 Next steps:")
                print("1. Restart your Python app in cPanel")
                print("2. Test login at: https://kodesql.in/auth/login/")
                print("3. UserDatabase will be created automatically for new users")
            else:
                print("\n⚠️  Table created but model has issues")
        else:
            print("\n❌ Failed to create table manually")
            print("\n🔄 Trying with Django migrations...")
            run_migrations()
    else:
        print("\n✅ Table exists - testing model...")
        if test_userdatabase_model():
            print("\n🎉 UserDatabase is working correctly!")
        else:
            print("\n⚠️  Table exists but model has issues")
            print("🔄 Running migrations to fix...")
            run_migrations()

if __name__ == "__main__":
    main()
