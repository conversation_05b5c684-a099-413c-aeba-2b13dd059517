# Fix Admin Panel - Complete Solution

## Current Problem
You're getting this error in the admin panel:
```
ProgrammingError: relation "users_passwordresettoken" does not exist
```

This prevents you from:
- Deleting users in admin panel
- Managing user accounts properly
- Accessing user-related functionality

## Root Cause
The `users_passwordresettoken` table is missing from your database. This table is required for:
- Password reset functionality
- Admin panel user management
- User deletion operations
- Token-based authentication features

## Solution: Two-Step Fix

### Step 1: Emergency Fix (Quick)
**Purpose**: Fix the immediate admin panel issue

**Upload and run:**
```bash
cd /home/<USER>/public_html/KodeSQL
python emergency_passwordreset_table_fix.py
```

**What it fixes:**
- ✅ Creates `users_passwordresettoken` table
- ✅ Creates `users_emailverificationtoken` table (if missing)
- ✅ Adds proper indexes for performance
- ✅ Tests token model functionality
- ✅ Fixes admin panel user deletion

### Step 2: Complete Fix (Comprehensive)
**Purpose**: Ensure all database tables exist

**Upload and run:**
```bash
cd /home/<USER>/public_html/KodeSQL
python fix_all_remaining_tables.py
```

**What it fixes:**
- ✅ All missing user-related tables
- ✅ All authentication tables
- ✅ All admin panel tables
- ✅ All many-to-many relationships
- ✅ Complete database integrity

## Quick Fix Instructions

### 1. Upload Scripts
Upload these files to `/home/<USER>/public_html/KodeSQL/`:
- `emergency_passwordreset_table_fix.py`
- `fix_all_remaining_tables.py`

### 2. Run Emergency Fix
```bash
cd /home/<USER>/public_html/KodeSQL
python emergency_passwordreset_table_fix.py
```

Expected output:
```
EMERGENCY FIX: users_passwordresettoken Table
SUCCESS: Django setup successful
SUCCESS: Database connection successful
MISSING: users_passwordresettoken
SUCCESS: users_passwordresettoken table created
SUCCESS: EMERGENCY FIX COMPLETE!
```

### 3. Restart Python App
1. cPanel → Python App
2. Find KodeSQL app
3. Click "Restart"
4. Wait for "Running" status

### 4. Test Admin Panel
- Visit: https://kodesql.in/admin/
- Go to Users section
- Try deleting a user
- Should work without errors

### 5. Run Complete Fix
```bash
python fix_all_remaining_tables.py
```

This ensures all tables are properly created.

## What Each Script Does

### emergency_passwordreset_table_fix.py
**Purpose:** Quick fix for admin panel
**Creates:**
- `users_passwordresettoken` table with proper structure
- `users_emailverificationtoken` table (if missing)
- Proper indexes for performance
- Tests model functionality

**Table Structure:**
```sql
CREATE TABLE users_passwordresettoken (
    id BIGSERIAL PRIMARY KEY,
    token UUID NOT NULL UNIQUE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL,
    is_used BOOLEAN NOT NULL DEFAULT FALSE,
    user_id BIGINT NOT NULL REFERENCES users_user(id)
);
```

### fix_all_remaining_tables.py
**Purpose:** Comprehensive database setup
**Creates:**
- All user model tables
- All authentication tables
- All admin panel tables
- All relationships and indexes
- Tests all functionality

## Expected Results

### After Emergency Fix:
- ✅ Admin panel loads without errors
- ✅ Can access Users section in admin
- ✅ Can delete users in admin panel
- ✅ Password reset functionality works
- ✅ User management works properly

### After Complete Fix:
- ✅ All Django functionality works
- ✅ All database tables exist
- ✅ All relationships work
- ✅ Complete admin panel functionality
- ✅ All user features work

## Manual Fix (If Scripts Fail)

If the scripts don't work, you can create the table manually:

### 1. Access cPanel PostgreSQL
1. Go to cPanel → phpPgAdmin
2. Select your `sqlplayground_main` database
3. Go to SQL tab

### 2. Run This SQL
```sql
-- Create password reset token table
CREATE TABLE IF NOT EXISTS users_passwordresettoken (
    id BIGSERIAL PRIMARY KEY,
    token UUID NOT NULL UNIQUE DEFAULT gen_random_uuid(),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    is_used BOOLEAN NOT NULL DEFAULT FALSE,
    user_id BIGINT NOT NULL REFERENCES users_user(id) ON DELETE CASCADE
);

-- Create indexes
CREATE INDEX IF NOT EXISTS users_passwordresettoken_user_id_idx 
ON users_passwordresettoken(user_id);

CREATE INDEX IF NOT EXISTS users_passwordresettoken_token_idx 
ON users_passwordresettoken(token);

-- Create email verification token table
CREATE TABLE IF NOT EXISTS users_emailverificationtoken (
    id BIGSERIAL PRIMARY KEY,
    token UUID NOT NULL UNIQUE DEFAULT gen_random_uuid(),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    is_used BOOLEAN NOT NULL DEFAULT FALSE,
    user_id BIGINT NOT NULL REFERENCES users_user(id) ON DELETE CASCADE
);

-- Create indexes
CREATE INDEX IF NOT EXISTS users_emailverificationtoken_user_id_idx 
ON users_emailverificationtoken(user_id);

CREATE INDEX IF NOT EXISTS users_emailverificationtoken_token_idx 
ON users_emailverificationtoken(token);
```

### 3. Restart Python App
Always restart after manual database changes.

## Troubleshooting

### If Emergency Fix Fails:
1. **Check database connection:**
   - Verify PostgreSQL credentials
   - Check database permissions

2. **Check table dependencies:**
   - Ensure `users_user` table exists
   - Run complete fix script

3. **Check logs:**
   - Look at cPanel error logs
   - Check script output for specific errors

### If Admin Panel Still Fails:
1. **Clear browser cache:**
   - Clear cookies for kodesql.in
   - Refresh admin panel

2. **Check other missing tables:**
   - Run complete fix script
   - Verify all tables exist

3. **Restart application:**
   - Always restart after database changes

## Success Verification

After the fix:

### Test Admin Panel:
- ✅ https://kodesql.in/admin/ loads
- ✅ Users section accessible
- ✅ Can view user list
- ✅ Can edit user details
- ✅ Can delete users without errors
- ✅ Password reset features work

### Test Database:
```bash
python -c "
import os, django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'sqlplayground.settings')
django.setup()
from users.models import PasswordResetToken, EmailVerificationToken
print(f'Password Reset Tokens: {PasswordResetToken.objects.count()}')
print(f'Email Verification Tokens: {EmailVerificationToken.objects.count()}')
print('All token models working!')
"
```

## Prevention

To prevent this in the future:
1. **Always run migrations after model changes**
2. **Use the provided scripts for deployment**
3. **Check migration status regularly**
4. **Test admin panel after deployments**

## Summary

This fix addresses the missing `users_passwordresettoken` table that was preventing admin panel functionality. The two-step approach ensures:
1. **Immediate relief** for admin panel issues
2. **Complete solution** for all database problems
3. **Proper functionality** for user management

Run the emergency fix first to restore admin functionality, then run the complete fix to ensure everything is properly set up.
