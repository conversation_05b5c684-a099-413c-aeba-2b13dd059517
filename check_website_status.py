#!/usr/bin/env python3
"""
Check Website Status - Diagnose any remaining issues
"""

import os
import sys
import django
from django.db import connection

def setup_django():
    """Setup Django environment"""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'sqlplayground.settings')
    django.setup()

def test_landing_page_view():
    """Test the landing page view that was causing the 500 error"""
    print("🔍 Testing Landing Page View...")
    
    try:
        from core.views import landing_page
        from django.test import RequestFactory
        
        # Create a fake request
        factory = RequestFactory()
        request = factory.get('/')
        
        # Try to call the view
        response = landing_page(request)
        
        print(f"   ✅ Landing page view works - Status: {response.status_code}")
        return True
        
    except Exception as e:
        print(f"   ❌ Landing page view failed: {e}")
        return False

def test_all_models():
    """Test all models to ensure they work"""
    print("🔍 Testing All Models...")
    
    try:
        from django.contrib.auth import get_user_model
        from challenges.models import Challenge, UserChallengeProgress
        from courses.models import Course
        from tutorials.models import Tutorial
        from core.models import SiteSettings
        from editor.models import QueryHistory
        
        User = get_user_model()
        
        # Test each model
        models_to_test = [
            (User, "User"),
            (Challenge, "Challenge"),
            (UserChallengeProgress, "UserChallengeProgress"),
            (Course, "Course"),
            (Tutorial, "Tutorial"),
            (SiteSettings, "SiteSettings"),
            (QueryHistory, "QueryHistory"),
        ]
        
        for model, name in models_to_test:
            try:
                count = model.objects.count()
                print(f"   ✅ {name}: {count} records")
            except Exception as e:
                print(f"   ❌ {name}: {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ Model testing failed: {e}")
        return False

def test_url_routing():
    """Test URL routing"""
    print("🔍 Testing URL Routing...")
    
    try:
        from django.urls import reverse
        from django.test import Client
        
        client = Client()
        
        # Test basic URLs
        test_urls = [
            ('/', 'Homepage'),
            ('/admin/', 'Admin'),
        ]
        
        for url, description in test_urls:
            try:
                response = client.get(url)
                print(f"   ✅ {description} ({url}): Status {response.status_code}")
            except Exception as e:
                print(f"   ❌ {description} ({url}): {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ URL routing test failed: {e}")
        return False

def check_static_files():
    """Check static files configuration"""
    print("🔍 Checking Static Files...")
    
    try:
        from django.conf import settings
        from django.contrib.staticfiles import finders
        
        print(f"   STATIC_URL: {settings.STATIC_URL}")
        print(f"   STATIC_ROOT: {settings.STATIC_ROOT}")
        
        # Check if static root exists
        if os.path.exists(settings.STATIC_ROOT):
            file_count = len([f for f in os.listdir(settings.STATIC_ROOT) if os.path.isfile(os.path.join(settings.STATIC_ROOT, f))])
            print(f"   ✅ STATIC_ROOT exists with {file_count} files")
        else:
            print(f"   ⚠️  STATIC_ROOT doesn't exist")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Static files check failed: {e}")
        return False

def check_database_connection():
    """Check database connections"""
    print("🔍 Checking Database Connections...")
    
    try:
        from django.db import connections
        
        # Test default database
        default_conn = connections['default']
        with default_conn.cursor() as cursor:
            cursor.execute("SELECT 1")
        print("   ✅ Default database connection working")
        
        # Test query databases if configured
        for db_name in ['query_mysql', 'query_postgres']:
            if db_name in connections.databases:
                try:
                    conn = connections[db_name]
                    with conn.cursor() as cursor:
                        cursor.execute("SELECT 1")
                    print(f"   ✅ {db_name} database connection working")
                except Exception as e:
                    print(f"   ⚠️  {db_name} database issue: {e}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Database connection test failed: {e}")
        return False

def create_debug_info():
    """Create debug information"""
    print("🔧 Creating Debug Information...")
    
    try:
        from django.conf import settings
        
        debug_info = f"""
Django Debug Information:
========================

DEBUG: {getattr(settings, 'DEBUG', 'Unknown')}
ALLOWED_HOSTS: {getattr(settings, 'ALLOWED_HOSTS', 'Unknown')}
SECRET_KEY: {'Set' if getattr(settings, 'SECRET_KEY', None) else 'Not Set'}

Database Configuration:
- Engine: {settings.DATABASES['default']['ENGINE']}
- Name: {settings.DATABASES['default']['NAME']}
- Host: {settings.DATABASES['default']['HOST']}

Static Files:
- STATIC_URL: {settings.STATIC_URL}
- STATIC_ROOT: {settings.STATIC_ROOT}

Installed Apps: {len(settings.INSTALLED_APPS)} apps
"""
        
        print(debug_info)
        return True
        
    except Exception as e:
        print(f"   ❌ Debug info creation failed: {e}")
        return False

def main():
    """Main function"""
    print("🚀 Website Status Check")
    print("="*50)
    
    # Setup Django
    try:
        setup_django()
        print("✅ Django setup successful")
    except Exception as e:
        print(f"❌ Django setup failed: {e}")
        return
    
    # Run all tests
    tests = [
        ("Database Connection", check_database_connection),
        ("All Models", test_all_models),
        ("Landing Page View", test_landing_page_view),
        ("URL Routing", test_url_routing),
        ("Static Files", check_static_files),
        ("Debug Information", create_debug_info)
    ]
    
    failed_tests = []
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}...")
        if not test_func():
            failed_tests.append(test_name)
    
    print("\n" + "="*50)
    print("📋 STATUS CHECK RESULTS")
    print("="*50)
    
    if failed_tests:
        print(f"\n⚠️  Issues found: {len(failed_tests)}")
        for test in failed_tests:
            print(f"   - {test}")
    else:
        print("\n✅ All tests passed!")
        print("Your website should be working correctly.")
    
    print("\n💡 If you're still seeing issues:")
    print("1. Check cPanel error logs")
    print("2. Share the exact error message you're seeing")
    print("3. Try accessing /admin/ directly")

if __name__ == "__main__":
    main()
