#!/usr/bin/env python3
"""
Fix all remaining missing database tables for admin panel
This specifically addresses the users_passwordresettoken and other missing tables
"""

import os
import sys

def setup_django():
    """Setup Django environment"""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'sqlplayground.settings')
    os.environ.setdefault('DEBUG', 'False')
    
    try:
        import django
        django.setup()
        print("SUCCESS: Django setup successful")
        return True
    except Exception as e:
        print(f"ERROR: Django setup failed: {e}")
        return False

def check_database_connection():
    """Check database connection"""
    print("Checking database connection...")
    
    try:
        from django.db import connection
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            if result:
                print("SUCCESS: Database connection successful")
                return True
    except Exception as e:
        print(f"ERROR: Database connection failed: {e}")
        return False

def get_all_missing_tables():
    """Check for all missing tables"""
    print("\nChecking for all missing tables...")
    
    try:
        from django.db import connection
        
        # All required tables for the users app
        required_tables = [
            'users_user',
            'users_user_groups',
            'users_user_user_permissions',
            'users_userprofile',
            'users_userdatabase',
            'users_emailverificationtoken',
            'users_passwordresettoken',  # This is the missing one causing current error
            'django_migrations',
            'django_content_type',
            'django_session',
            'auth_permission',
            'auth_group',
            'auth_group_permissions',
            'django_admin_log',
            'django_site',
        ]
        
        missing = []
        existing = []
        
        with connection.cursor() as cursor:
            for table in required_tables:
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_name = %s
                    )
                """, [table])
                
                exists = cursor.fetchone()[0]
                if exists:
                    existing.append(table)
                    print(f"EXISTS: {table}")
                else:
                    missing.append(table)
                    print(f"MISSING: {table}")
        
        return missing, existing
        
    except Exception as e:
        print(f"ERROR: Table check failed: {e}")
        return [], []

def create_missing_user_tables():
    """Create all missing user-related tables"""
    print("\nCreating missing user tables...")
    
    try:
        from django.db import connection
        
        # SQL commands to create all missing tables
        sql_commands = [
            # Email verification token table
            """
            CREATE TABLE IF NOT EXISTS users_emailverificationtoken (
                id BIGSERIAL PRIMARY KEY,
                token UUID NOT NULL UNIQUE DEFAULT gen_random_uuid(),
                created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                is_used BOOLEAN NOT NULL DEFAULT FALSE,
                user_id BIGINT NOT NULL REFERENCES users_user(id) ON DELETE CASCADE
            );
            CREATE INDEX IF NOT EXISTS users_emailverificationtoken_user_id_idx ON users_emailverificationtoken(user_id);
            CREATE INDEX IF NOT EXISTS users_emailverificationtoken_token_idx ON users_emailverificationtoken(token);
            """,
            
            # Password reset token table (THE MISSING TABLE CAUSING CURRENT ERROR)
            """
            CREATE TABLE IF NOT EXISTS users_passwordresettoken (
                id BIGSERIAL PRIMARY KEY,
                token UUID NOT NULL UNIQUE DEFAULT gen_random_uuid(),
                created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                is_used BOOLEAN NOT NULL DEFAULT FALSE,
                user_id BIGINT NOT NULL REFERENCES users_user(id) ON DELETE CASCADE
            );
            CREATE INDEX IF NOT EXISTS users_passwordresettoken_user_id_idx ON users_passwordresettoken(user_id);
            CREATE INDEX IF NOT EXISTS users_passwordresettoken_token_idx ON users_passwordresettoken(token);
            """,
            
            # Ensure users_user table exists with all fields
            """
            CREATE TABLE IF NOT EXISTS users_user (
                id BIGSERIAL PRIMARY KEY,
                password VARCHAR(128) NOT NULL,
                last_login TIMESTAMP WITH TIME ZONE,
                is_superuser BOOLEAN NOT NULL DEFAULT FALSE,
                username VARCHAR(150) UNIQUE NOT NULL,
                first_name VARCHAR(150) NOT NULL DEFAULT '',
                last_name VARCHAR(150) NOT NULL DEFAULT '',
                email VARCHAR(254) UNIQUE NOT NULL,
                is_staff BOOLEAN NOT NULL DEFAULT FALSE,
                is_active BOOLEAN NOT NULL DEFAULT TRUE,
                date_joined TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                is_email_verified BOOLEAN NOT NULL DEFAULT FALSE,
                created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
            );
            """,
            
            # User profile table
            """
            CREATE TABLE IF NOT EXISTS users_userprofile (
                id BIGSERIAL PRIMARY KEY,
                bio TEXT NOT NULL DEFAULT '',
                location VARCHAR(100) NOT NULL DEFAULT '',
                birth_date DATE,
                profile_picture VARCHAR(100) NOT NULL DEFAULT '',
                theme_preference VARCHAR(10) NOT NULL DEFAULT 'light',
                total_xp INTEGER NOT NULL DEFAULT 0,
                created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                user_id BIGINT NOT NULL UNIQUE REFERENCES users_user(id) ON DELETE CASCADE
            );
            """,
            
            # User database table
            """
            CREATE TABLE IF NOT EXISTS users_userdatabase (
                id BIGSERIAL PRIMARY KEY,
                database_path VARCHAR(255) NOT NULL,
                created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                last_accessed TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                user_id BIGINT NOT NULL UNIQUE REFERENCES users_user(id) ON DELETE CASCADE
            );
            """,
        ]
        
        with connection.cursor() as cursor:
            for i, sql in enumerate(sql_commands, 1):
                try:
                    cursor.execute(sql)
                    print(f"SUCCESS: User table {i}/{len(sql_commands)} created/verified")
                except Exception as e:
                    print(f"WARNING: User table {i} issue: {e}")
        
        return True
        
    except Exception as e:
        print(f"ERROR: User table creation failed: {e}")
        return False

def create_missing_auth_tables():
    """Create missing authentication and admin tables"""
    print("\nCreating missing auth/admin tables...")
    
    try:
        from django.db import connection
        
        sql_commands = [
            # Django content types
            """
            CREATE TABLE IF NOT EXISTS django_content_type (
                id BIGSERIAL PRIMARY KEY,
                app_label VARCHAR(100) NOT NULL,
                model VARCHAR(100) NOT NULL,
                UNIQUE(app_label, model)
            );
            """,
            
            # Auth group
            """
            CREATE TABLE IF NOT EXISTS auth_group (
                id BIGSERIAL PRIMARY KEY,
                name VARCHAR(150) UNIQUE NOT NULL
            );
            """,
            
            # Auth permission
            """
            CREATE TABLE IF NOT EXISTS auth_permission (
                id BIGSERIAL PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                content_type_id BIGINT REFERENCES django_content_type(id),
                codename VARCHAR(100) NOT NULL,
                UNIQUE(content_type_id, codename)
            );
            """,
            
            # Auth group permissions
            """
            CREATE TABLE IF NOT EXISTS auth_group_permissions (
                id BIGSERIAL PRIMARY KEY,
                group_id BIGINT NOT NULL REFERENCES auth_group(id) ON DELETE CASCADE,
                permission_id BIGINT NOT NULL REFERENCES auth_permission(id) ON DELETE CASCADE,
                UNIQUE(group_id, permission_id)
            );
            """,
            
            # Users user groups (many-to-many)
            """
            CREATE TABLE IF NOT EXISTS users_user_groups (
                id BIGSERIAL PRIMARY KEY,
                user_id BIGINT NOT NULL REFERENCES users_user(id) ON DELETE CASCADE,
                group_id BIGINT NOT NULL REFERENCES auth_group(id) ON DELETE CASCADE,
                UNIQUE(user_id, group_id)
            );
            """,
            
            # Users user permissions (many-to-many)
            """
            CREATE TABLE IF NOT EXISTS users_user_user_permissions (
                id BIGSERIAL PRIMARY KEY,
                user_id BIGINT NOT NULL REFERENCES users_user(id) ON DELETE CASCADE,
                permission_id BIGINT NOT NULL REFERENCES auth_permission(id) ON DELETE CASCADE,
                UNIQUE(user_id, permission_id)
            );
            """,
            
            # Django admin log
            """
            CREATE TABLE IF NOT EXISTS django_admin_log (
                id BIGSERIAL PRIMARY KEY,
                action_time TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                object_id TEXT,
                object_repr VARCHAR(200) NOT NULL,
                action_flag SMALLINT NOT NULL,
                change_message TEXT NOT NULL,
                content_type_id BIGINT REFERENCES django_content_type(id),
                user_id BIGINT NOT NULL REFERENCES users_user(id) ON DELETE CASCADE
            );
            """,
            
            # Django sessions
            """
            CREATE TABLE IF NOT EXISTS django_session (
                session_key VARCHAR(40) PRIMARY KEY,
                session_data TEXT NOT NULL,
                expire_date TIMESTAMP WITH TIME ZONE NOT NULL
            );
            CREATE INDEX IF NOT EXISTS django_session_expire_date_idx ON django_session(expire_date);
            """,
            
            # Django site
            """
            CREATE TABLE IF NOT EXISTS django_site (
                id BIGSERIAL PRIMARY KEY,
                domain VARCHAR(100) NOT NULL UNIQUE,
                name VARCHAR(50) NOT NULL
            );
            INSERT INTO django_site (id, domain, name) VALUES (1, 'kodesql.in', 'KodeSQL') ON CONFLICT (id) DO NOTHING;
            """,
        ]
        
        with connection.cursor() as cursor:
            for i, sql in enumerate(sql_commands, 1):
                try:
                    cursor.execute(sql)
                    print(f"SUCCESS: Auth table {i}/{len(sql_commands)} created/verified")
                except Exception as e:
                    print(f"WARNING: Auth table {i} issue: {e}")
        
        return True
        
    except Exception as e:
        print(f"ERROR: Auth table creation failed: {e}")
        return False

def test_admin_functionality():
    """Test if admin panel functionality works"""
    print("\nTesting admin functionality...")
    
    try:
        from django.contrib.auth import get_user_model
        from django.contrib.auth.models import Group, Permission
        from users.models import PasswordResetToken, EmailVerificationToken
        
        User = get_user_model()
        
        # Test basic queries
        user_count = User.objects.count()
        print(f"SUCCESS: Can query users: {user_count} users")
        
        group_count = Group.objects.count()
        print(f"SUCCESS: Can query groups: {group_count} groups")
        
        permission_count = Permission.objects.count()
        print(f"SUCCESS: Can query permissions: {permission_count} permissions")
        
        # Test the problematic model
        token_count = PasswordResetToken.objects.count()
        print(f"SUCCESS: Can query password reset tokens: {token_count} tokens")
        
        email_token_count = EmailVerificationToken.objects.count()
        print(f"SUCCESS: Can query email verification tokens: {email_token_count} tokens")
        
        return True
        
    except Exception as e:
        print(f"ERROR: Admin functionality test failed: {e}")
        return False

def main():
    """Main function to fix all remaining database issues"""
    print("FIXING ALL REMAINING DATABASE TABLES")
    print("=" * 50)
    print("Specifically fixing: users_passwordresettoken table")
    
    # Setup Django
    if not setup_django():
        return
    
    # Check database connection
    if not check_database_connection():
        return
    
    # Check missing tables
    missing_tables, existing_tables = get_all_missing_tables()
    
    if missing_tables:
        print(f"\nFound {len(missing_tables)} missing tables")
        print(f"Existing tables: {len(existing_tables)}")
        
        # Create missing user tables
        if create_missing_user_tables():
            print("SUCCESS: User tables created")
        
        # Create missing auth tables
        if create_missing_auth_tables():
            print("SUCCESS: Auth tables created")
        
        # Test admin functionality
        if test_admin_functionality():
            print("\nSUCCESS: All database tables are now working!")
            print("\nNext steps:")
            print("1. Restart your Python app in cPanel")
            print("2. Test admin panel: https://kodesql.in/admin/")
            print("3. Try deleting users in admin panel")
            print("4. All admin functionality should now work")
        else:
            print("\nWARNING: Tables created but some functionality issues remain")
    else:
        print("\nAll required tables already exist")
        
        # Still test functionality
        if test_admin_functionality():
            print("SUCCESS: All functionality working")
        else:
            print("WARNING: Some functionality issues detected")

if __name__ == "__main__":
    main()
