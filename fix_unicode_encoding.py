#!/usr/bin/env python3
"""
Fix Unicode encoding issues in users/views.py
Remove emoji characters that cause ASCII encoding errors in cPanel environment
"""

import re
import os

def fix_unicode_in_views():
    """Fix Unicode encoding issues in users/views.py"""
    print("🔧 Fixing Unicode encoding issues in users/views.py...")
    
    file_path = 'users/views.py'
    
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        return False
    
    try:
        # Read the file with UTF-8 encoding
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Define emoji and Unicode character replacements
        unicode_replacements = {
            # Emojis in print statements
            '✅': 'SUCCESS:',
            '❌': 'ERROR:',
            '🎉': '',
            '⚠️': 'WARNING:',
            '🔍': '',
            '📧': '',
            '💡': '',
            '🔧': '',
            '🗑️': '',
            'ℹ️': 'INFO:',
            '🖼️': '',
            '🔐': '',
            '🚨': 'ALERT:',
            
            # Unicode characters in messages
            '⚠️ ': 'WARNING: ',
        }
        
        # Apply replacements
        for unicode_char, replacement in unicode_replacements.items():
            content = content.replace(unicode_char, replacement)
        
        # Clean up extra spaces and formatting
        content = re.sub(r'  +', ' ', content)  # Replace multiple spaces with single space
        content = re.sub(r"print\(f\"SUCCESS: ", r'print(f"SUCCESS: ', content)
        content = re.sub(r"print\(f\"ERROR: ", r'print(f"ERROR: ', content)
        content = re.sub(r"print\(f\"WARNING: ", r'print(f"WARNING: ', content)
        
        # Fix specific problematic lines
        problematic_patterns = [
            (r"messages\.success\(request, f' Welcome", r"messages.success(request, f'Welcome"),
            (r"messages\.warning\(request, 'WARNING: Registration", r"messages.warning(request, 'WARNING: Registration"),
            (r"messages\.success\(request, ' Verification", r"messages.success(request, 'Verification"),
        ]
        
        for pattern, replacement in problematic_patterns:
            content = re.sub(pattern, replacement, content)
        
        # Write back to file with UTF-8 encoding
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("SUCCESS: Fixed Unicode encoding issues in users/views.py")
        return True
        
    except Exception as e:
        print(f"ERROR: Failed to fix Unicode issues: {e}")
        return False

def fix_unicode_in_messages():
    """Fix Unicode characters in Django messages"""
    print("\n🔧 Fixing Unicode in Django messages...")
    
    file_path = 'users/views.py'
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Fix specific message lines that contain Unicode
        message_fixes = [
            # Line 126 - Registration warning message
            (
                r"messages\.warning\(request, 'WARNING: Registration successful! However, there was an issue sending the verification email\. Please contact support or try logging in\.'\)",
                r"messages.warning(request, 'Registration successful! However, there was an issue sending the verification email. Please contact support or try logging in.')"
            ),
            # Line 203 - Email verification success message
            (
                r"messages\.success\(request, f' Email verified successfully! Welcome to KodeSQL, \{user\.first_name or user\.username\}! You can now log in and start learning\.'\)",
                r"messages.success(request, f'Email verified successfully! Welcome to KodeSQL, {user.first_name or user.username}! You can now log in and start learning.')"
            ),
            # Line 468 - Verification email sent message
            (
                r"messages\.success\(request, ' Verification email sent! Please check your inbox\.'\)",
                r"messages.success(request, 'Verification email sent! Please check your inbox.')"
            ),
        ]
        
        for pattern, replacement in message_fixes:
            content = re.sub(pattern, replacement, content)
        
        # Write back to file
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("SUCCESS: Fixed Unicode in Django messages")
        return True
        
    except Exception as e:
        print(f"ERROR: Failed to fix message Unicode: {e}")
        return False

def verify_no_unicode():
    """Verify that no problematic Unicode characters remain"""
    print("\n🔍 Verifying Unicode fix...")
    
    file_path = 'users/views.py'
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for remaining problematic Unicode characters
        problematic_chars = ['✅', '❌', '🎉', '⚠️', '🔍', '📧', '💡', '🔧', '🗑️', 'ℹ️', '🖼️', '🔐', '🚨']
        
        found_issues = []
        for char in problematic_chars:
            if char in content:
                # Find line numbers
                lines = content.split('\n')
                for i, line in enumerate(lines, 1):
                    if char in line:
                        found_issues.append(f"Line {i}: {char} in '{line.strip()}'")
        
        if found_issues:
            print("ERROR: Still found problematic Unicode characters:")
            for issue in found_issues[:5]:  # Show first 5 issues
                print(f"   {issue}")
            return False
        else:
            print("SUCCESS: No problematic Unicode characters found")
            return True
            
    except Exception as e:
        print(f"ERROR: Verification failed: {e}")
        return False

def create_backup():
    """Create backup of original file"""
    file_path = 'users/views.py'
    backup_path = 'users/views.py.backup'
    
    try:
        if os.path.exists(file_path) and not os.path.exists(backup_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            with open(backup_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print("SUCCESS: Created backup at users/views.py.backup")
        return True
    except Exception as e:
        print(f"ERROR: Failed to create backup: {e}")
        return False

def main():
    """Main function to fix Unicode encoding issues"""
    print("🚀 Fixing Unicode Encoding Issues")
    print("=" * 40)
    
    # Create backup first
    create_backup()
    
    # Fix Unicode issues
    fixes = [
        ("Unicode Characters in Print Statements", fix_unicode_in_views),
        ("Unicode Characters in Django Messages", fix_unicode_in_messages),
        ("Verification", verify_no_unicode),
    ]
    
    results = []
    for name, fix_func in fixes:
        print(f"\n--- {name} ---")
        try:
            result = fix_func()
            results.append((name, result))
        except Exception as e:
            print(f"ERROR: {name} failed: {e}")
            results.append((name, False))
    
    # Summary
    print("\n" + "=" * 40)
    print("📋 FIX RESULTS:")
    
    all_passed = True
    for name, result in results:
        status = "SUCCESS" if result else "ERROR"
        print(f"{status}: {name}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 Unicode encoding issues fixed!")
        print("\n📋 Next steps:")
        print("1. Upload the fixed users/views.py to cPanel")
        print("2. Restart your Python app in cPanel")
        print("3. Test signup at: https://kodesql.in/auth/register/")
        print("4. Registration should now work without Unicode errors")
    else:
        print("\nERROR: Some fixes failed - check above for details")
        print("You may need to manually edit the problematic lines")

if __name__ == "__main__":
    main()
