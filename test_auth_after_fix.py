#!/usr/bin/env python3
"""
Test authentication functionality after applying fixes
"""

import os
import sys
import django
import requests
from urllib.parse import urljoin

def setup_django():
    """Setup Django environment for testing"""
    sys.path.insert(0, os.getcwd())
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'sqlplayground.settings')
    django.setup()

def test_django_setup():
    """Test Django configuration"""
    print("🔧 Testing Django Setup...")
    
    try:
        setup_django()
        from django.conf import settings
        
        print(f"✅ Django version: {django.get_version()}")
        print(f"✅ Debug mode: {settings.DEBUG}")
        print(f"✅ Allowed hosts: {settings.ALLOWED_HOSTS}")
        print(f"✅ CSRF trusted origins: {settings.CSRF_TRUSTED_ORIGINS}")
        
        return True
    except Exception as e:
        print(f"❌ Django setup failed: {e}")
        return False

def test_database_connection():
    """Test database connectivity"""
    print("\n🔧 Testing Database Connection...")
    
    try:
        from django.db import connection
        
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
        
        print("✅ Database connection successful")
        return True
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

def test_models():
    """Test model imports and basic operations"""
    print("\n🔧 Testing Models...")
    
    try:
        from users.models import User, UserProfile, UserDatabase
        from django.contrib.sites.models import Site
        
        # Test model imports
        print("✅ User models imported successfully")
        
        # Test Site configuration
        site = Site.objects.get(id=1)
        print(f"✅ Site configured: {site.domain}")
        
        # Test user count
        user_count = User.objects.count()
        print(f"✅ Users in database: {user_count}")
        
        return True
    except Exception as e:
        print(f"❌ Models test failed: {e}")
        return False

def test_forms():
    """Test form imports and instantiation"""
    print("\n🔧 Testing Forms...")
    
    try:
        from users.forms import UserLoginForm, UserRegistrationForm
        
        # Test form instantiation
        login_form = UserLoginForm()
        register_form = UserRegistrationForm()
        
        print("✅ Login form created successfully")
        print("✅ Registration form created successfully")
        
        return True
    except Exception as e:
        print(f"❌ Forms test failed: {e}")
        return False

def test_views():
    """Test view imports and basic functionality"""
    print("\n🔧 Testing Views...")
    
    try:
        from django.test import RequestFactory
        from django.contrib.messages.storage.fallback import FallbackStorage
        from users.views import login_view, register_view
        
        factory = RequestFactory()
        
        # Test login view
        request = factory.get('/auth/login/')
        request.user = None
        request.session = {}
        setattr(request, '_messages', FallbackStorage(request))
        
        response = login_view(request)
        print(f"✅ Login view response: {response.status_code}")
        
        # Test register view
        request = factory.get('/auth/register/')
        request.user = None
        request.session = {}
        setattr(request, '_messages', FallbackStorage(request))
        
        response = register_view(request)
        print(f"✅ Register view response: {response.status_code}")
        
        return True
    except Exception as e:
        print(f"❌ Views test failed: {e}")
        return False

def test_static_files():
    """Test static files configuration"""
    print("\n🔧 Testing Static Files...")
    
    try:
        from django.conf import settings
        from django.contrib.staticfiles import finders
        
        print(f"✅ Static URL: {settings.STATIC_URL}")
        print(f"✅ Static root: {settings.STATIC_ROOT}")
        print(f"✅ Media URL: {settings.MEDIA_URL}")
        print(f"✅ Media root: {settings.MEDIA_ROOT}")
        
        # Test if we can find a static file
        css_file = finders.find('css/style.css')
        if css_file:
            print("✅ Static file finder working")
        else:
            print("⚠️  Static files may need to be collected")
        
        return True
    except Exception as e:
        print(f"❌ Static files test failed: {e}")
        return False

def test_url_patterns():
    """Test URL configuration"""
    print("\n🔧 Testing URL Patterns...")
    
    try:
        from django.urls import reverse
        
        # Test auth URLs
        login_url = reverse('users:login')
        register_url = reverse('users:register')
        
        print(f"✅ Login URL: {login_url}")
        print(f"✅ Register URL: {register_url}")
        
        return True
    except Exception as e:
        print(f"❌ URL patterns test failed: {e}")
        return False

def test_live_urls(base_url="https://kodesql.in"):
    """Test live URLs if accessible"""
    print(f"\n🔧 Testing Live URLs at {base_url}...")
    
    try:
        urls_to_test = [
            "/auth/login/",
            "/auth/register/",
            "/admin/",
        ]
        
        for url_path in urls_to_test:
            full_url = urljoin(base_url, url_path)
            try:
                response = requests.get(full_url, timeout=10)
                if response.status_code == 200:
                    print(f"✅ {url_path}: {response.status_code}")
                elif response.status_code in [301, 302]:
                    print(f"🔄 {url_path}: {response.status_code} (redirect)")
                else:
                    print(f"⚠️  {url_path}: {response.status_code}")
            except requests.RequestException as e:
                print(f"❌ {url_path}: Connection failed - {e}")
        
        return True
    except Exception as e:
        print(f"❌ Live URL test failed: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 KodeSQL Authentication Test Suite")
    print("=" * 50)
    
    tests = [
        ("Django Setup", test_django_setup),
        ("Database Connection", test_database_connection),
        ("Models", test_models),
        ("Forms", test_forms),
        ("Views", test_views),
        ("Static Files", test_static_files),
        ("URL Patterns", test_url_patterns),
        ("Live URLs", test_live_urls),
    ]
    
    results = []
    for name, test_func in tests:
        print(f"\n--- {name} ---")
        try:
            result = test_func()
            results.append((name, result))
        except Exception as e:
            print(f"❌ {name} failed with exception: {e}")
            results.append((name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 TEST RESULTS SUMMARY:")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for name, result in results:
        status = "✅" if result else "❌"
        print(f"{status} {name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed >= total - 1:  # Allow one test to fail
        print("\n🎉 Authentication system looks good!")
        print("\n📋 Next Steps:")
        print("1. Test login/signup manually")
        print("2. Create a test user account")
        print("3. Verify email functionality")
    else:
        print("\n❌ Issues found. Check the failed tests above.")

if __name__ == "__main__":
    main()
