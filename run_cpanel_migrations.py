#!/usr/bin/env python3
"""
Simple script to run Django migrations on cPanel
Use this when you need to apply migrations to your production database
"""

import os
import sys

def setup_django():
    """Setup Django environment for cPanel"""
    # Set Django settings
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'sqlplayground.settings')
    
    # Set production environment
    os.environ.setdefault('DEBUG', 'False')
    
    try:
        import django
        django.setup()
        print("✅ Django setup successful")
        return True
    except Exception as e:
        print(f"❌ Django setup failed: {e}")
        return False

def run_migrations():
    """Run Django migrations"""
    print("🔄 Running Django migrations...")
    
    try:
        from django.core.management import execute_from_command_line
        
        # Run migrations with verbose output
        print("\n--- Running migrations ---")
        execute_from_command_line(['manage.py', 'migrate', '--verbosity=2'])
        print("✅ Migrations completed successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        print("\nTrying individual app migrations...")
        
        # Try migrating apps individually
        apps = ['users', 'core', 'challenges', 'tutorials', 'courses', 'editor']
        
        for app in apps:
            try:
                print(f"\n--- Migrating {app} ---")
                execute_from_command_line(['manage.py', 'migrate', app, '--verbosity=1'])
                print(f"✅ {app} migrated successfully")
            except Exception as app_error:
                print(f"⚠️  {app} migration failed: {app_error}")
        
        return False

def check_migration_status():
    """Check current migration status"""
    print("\n📋 Checking migration status...")
    
    try:
        from django.core.management import execute_from_command_line
        
        print("\n--- Migration Status ---")
        execute_from_command_line(['manage.py', 'showmigrations'])
        
        return True
        
    except Exception as e:
        print(f"❌ Could not check migration status: {e}")
        return False

def test_database_connection():
    """Test database connection"""
    print("🔍 Testing database connection...")
    
    try:
        from django.db import connection
        
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            
            if result:
                print("✅ Database connection successful")
                
                # Check if users table exists
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_name = 'users_user'
                    )
                """)
                
                if cursor.fetchone()[0]:
                    print("✅ users_user table exists")
                else:
                    print("⚠️  users_user table missing - migrations needed")
                
                return True
            else:
                print("❌ Database connection failed")
                return False
                
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False

def create_superuser_prompt():
    """Prompt to create superuser if none exists"""
    print("\n👤 Checking for superuser...")
    
    try:
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        
        if User.objects.filter(is_superuser=True).exists():
            print("✅ Superuser already exists")
        else:
            print("⚠️  No superuser found")
            print("\n📝 To create a superuser, run:")
            print("   python manage.py createsuperuser")
            print("\n   Or use this script:")
            print("   python -c \"")
            print("   import os, django")
            print("   os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'sqlplayground.settings')")
            print("   django.setup()")
            print("   from django.contrib.auth import get_user_model")
            print("   User = get_user_model()")
            print("   User.objects.create_superuser('admin', '<EMAIL>', 'your_password')")
            print("   print('Superuser created!')")
            print("   \"")
        
    except Exception as e:
        print(f"❌ Superuser check failed: {e}")

def main():
    """Main function"""
    print("🚀 cPanel Django Migration Runner")
    print("=" * 40)
    
    # Setup Django
    if not setup_django():
        print("\n❌ Cannot proceed without Django setup")
        return
    
    # Test database connection
    if not test_database_connection():
        print("\n❌ Cannot proceed without database connection")
        print("\n🔧 Check your database settings:")
        print("1. Verify database exists in cPanel")
        print("2. Check database credentials")
        print("3. Ensure database user has proper permissions")
        return
    
    # Check current migration status
    check_migration_status()
    
    # Run migrations
    if run_migrations():
        print("\n🎉 Migrations completed successfully!")
        
        # Check for superuser
        create_superuser_prompt()
        
        print("\n📋 Next steps:")
        print("1. Restart your Python app in cPanel")
        print("2. Test login at: https://kodesql.in/auth/login/")
        print("3. Check admin panel: https://kodesql.in/admin/")
        
    else:
        print("\n⚠️  Some migrations failed")
        print("\n🔧 Manual fixes you can try:")
        print("1. Run: python fix_cpanel_database_migration.py")
        print("2. Check cPanel error logs")
        print("3. Verify database permissions")

if __name__ == "__main__":
    main()
