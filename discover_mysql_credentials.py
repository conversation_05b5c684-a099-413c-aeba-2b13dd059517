#!/usr/bin/env python3
"""
MySQL Credentials Discovery for cPanel
This script helps find the correct MySQL credentials on your cPanel server
"""

import os
import sys

def check_environment_variables():
    """Check all MySQL-related environment variables"""
    print("🔍 Checking Environment Variables...")
    
    mysql_vars = [
        'QUERY_MYSQL_HOST',
        'QUERY_MYSQL_PORT', 
        'QUERY_MYSQL_USER',
        'QUERY_MYSQL_PASSWORD',
        'QUERY_MYSQL_DB_NAME',
        'PRIMARY_DB_USER',
        'PRIMARY_DB_NAME'
    ]
    
    for var in mysql_vars:
        value = os.environ.get(var, 'NOT SET')
        if 'PASSWORD' in var and value != 'NOT SET':
            print(f"  {var}: [HIDDEN]")
        else:
            print(f"  {var}: {value}")

def test_mysql_variations():
    """Try different MySQL username variations common in cPanel"""
    print("\n🔍 Testing MySQL Username Variations...")
    
    try:
        import pymysql
    except ImportError:
        print("❌ PyMySQL not available")
        return
    
    # Common cPanel username patterns
    base_username = "kodesqli"
    password = os.environ.get('QUERY_MYSQL_PASSWORD', 'forgex99')
    database = os.environ.get('QUERY_MYSQL_DB_NAME', 'kodesqli_queries_mysql')
    
    username_variations = [
        f"{base_username}_mysql",
        f"{base_username}_kodesql_mysql_user", 
        f"{base_username}_main_database",
        f"{base_username}_main_database2",
        f"{base_username}_postgres",
        base_username,
        "root"
    ]
    
    database_variations = [
        f"{base_username}_queries_mysql",
        f"{base_username}_main_database",
        f"{base_username}_sqlplayground_queries_mysql",
        database
    ]
    
    print(f"Testing with password: [HIDDEN]")
    print(f"Testing databases: {database_variations}")
    print()
    
    for username in username_variations:
        for db_name in database_variations:
            try:
                print(f"  Trying: {username} -> {db_name}")
                conn = pymysql.connect(
                    host='localhost',
                    user=username,
                    password=password,
                    database=db_name,
                    connect_timeout=5
                )
                
                cursor = conn.cursor()
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                cursor.close()
                conn.close()
                
                print(f"  ✅ SUCCESS! Username: {username}, Database: {db_name}")
                return username, db_name
                
            except Exception as e:
                error_msg = str(e)
                if "Access denied" in error_msg:
                    print(f"    ❌ Access denied")
                elif "Unknown database" in error_msg:
                    print(f"    ⚠️  Database doesn't exist")
                else:
                    print(f"    ❌ {error_msg}")
    
    print("❌ No working MySQL credentials found")
    return None, None

def check_django_database_config():
    """Check Django database configuration"""
    print("\n🔍 Checking Django Database Configuration...")
    
    try:
        # Setup Django
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'sqlplayground.settings')
        import django
        django.setup()
        
        from django.conf import settings
        
        # Check database settings
        databases = settings.DATABASES
        
        for db_name, config in databases.items():
            print(f"\n  Database: {db_name}")
            print(f"    Engine: {config.get('ENGINE', 'Not set')}")
            print(f"    Name: {config.get('NAME', 'Not set')}")
            print(f"    Host: {config.get('HOST', 'Not set')}")
            print(f"    User: {config.get('USER', 'Not set')}")
            print(f"    Port: {config.get('PORT', 'Not set')}")
        
        # Try Django database connections
        print("\n  Testing Django Connections:")
        from django.db import connections
        
        for db_name in ['default', 'query_mysql', 'query_postgres']:
            try:
                if db_name in connections.databases:
                    conn = connections[db_name]
                    with conn.cursor() as cursor:
                        cursor.execute("SELECT 1")
                    print(f"    ✅ {db_name}: Connected")
                else:
                    print(f"    ⚠️  {db_name}: Not configured")
            except Exception as e:
                print(f"    ❌ {db_name}: {e}")
                
    except Exception as e:
        print(f"❌ Django setup failed: {e}")

def suggest_fixes(working_username, working_database):
    """Suggest fixes based on findings"""
    print("\n" + "="*60)
    print("📋 RECOMMENDATIONS")
    print("="*60)
    
    if working_username and working_database:
        print(f"✅ Found working credentials!")
        print(f"   Username: {working_username}")
        print(f"   Database: {working_database}")
        print()
        print("🔧 Update your .env file with:")
        print(f"   QUERY_MYSQL_USER={working_username}")
        print(f"   QUERY_MYSQL_DB_NAME={working_database}")
        print()
        print("Then test the challenge solve page again.")
        
    else:
        print("❌ No working MySQL credentials found.")
        print()
        print("🔧 Next steps:")
        print("1. Check cPanel MySQL Databases section")
        print("2. Verify the exact database and username")
        print("3. Ensure the user has ALL PRIVILEGES on the database")
        print("4. Check if the password is correct")
        print()
        print("Common cPanel issues:")
        print("- Database names have username prefix (e.g., kodesqli_dbname)")
        print("- Usernames have username prefix (e.g., kodesqli_username)")
        print("- User must be added to database with proper privileges")

def main():
    print("🚀 MySQL Credentials Discovery for cPanel")
    print("="*60)
    
    # Load environment variables
    try:
        from dotenv import load_dotenv
        load_dotenv()
        print("✅ Environment variables loaded from .env")
    except:
        print("⚠️  Could not load .env file, using system environment")
    
    # Run discovery steps
    check_environment_variables()
    working_username, working_database = test_mysql_variations()
    check_django_database_config()
    suggest_fixes(working_username, working_database)

if __name__ == "__main__":
    main()
