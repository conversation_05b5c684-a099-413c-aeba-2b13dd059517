#!/usr/bin/env python3
"""
Test PostgreSQL Fix
Test the PostgreSQL connection with correct credentials and table prefix approach
"""

import os
import sys

def test_postgresql_connection():
    """Test PostgreSQL connection with correct credentials"""
    print("🔍 Testing PostgreSQL Connection...")
    
    try:
        import psycopg2
        from psycopg2.extras import RealDictCursor
        
        # Use the working credentials (same as default database)
        conn = psycopg2.connect(
            host='127.0.0.1',
            port=5432,
            user='kodesqli_main_database',
            password='forgex99',
            database='kodesqli_postgres',
            cursor_factory=RealDictCursor
        )
        
        cursor = conn.cursor()
        
        # Test basic query
        cursor.execute("SELECT 1 as test")
        result = cursor.fetchone()
        print(f"✅ Basic PostgreSQL query successful: {result}")
        
        # Test table creation with prefix (what the challenge system will do)
        test_prefix = "challenge_test_pg_12345"
        
        cursor.execute(f'''
            CREATE TABLE IF NOT EXISTS "{test_prefix}_employees" (
                id INTEGER PRIMARY KEY,
                name VARCHAR(100),
                department VARCHAR(50)
            )
        ''')
        print("✅ PostgreSQL table creation with prefix successful")
        
        # Insert test data
        cursor.execute(f'''
            INSERT INTO "{test_prefix}_employees" (id, name, department) 
            VALUES (1, 'John Doe', 'Engineering')
        ''')
        print("✅ PostgreSQL insert with prefix successful")
        
        # Query test data
        cursor.execute(f'SELECT * FROM "{test_prefix}_employees"')
        results = cursor.fetchall()
        print(f"✅ PostgreSQL query with prefix successful: {results}")
        
        # Cleanup
        cursor.execute(f'DROP TABLE IF EXISTS "{test_prefix}_employees"')
        print("✅ PostgreSQL cleanup successful")
        
        cursor.close()
        conn.close()
        
        print("🎉 PostgreSQL test with table prefixes PASSED!")
        return True
        
    except Exception as e:
        print(f"❌ PostgreSQL test failed: {e}")
        return False

def test_django_postgresql_connection():
    """Test Django PostgreSQL connection"""
    print("\n🔍 Testing Django PostgreSQL Connection...")
    
    try:
        # Setup Django
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'sqlplayground.settings')
        import django
        django.setup()
        
        from django.db import connections
        
        # Test PostgreSQL connection through Django
        pg_conn = connections['query_postgres']
        with pg_conn.cursor() as cursor:
            cursor.execute("SELECT 1 as test")
            result = cursor.fetchone()
            print(f"✅ Django PostgreSQL connection successful: {result}")
            return True
            
    except Exception as e:
        print(f"❌ Django PostgreSQL connection failed: {e}")
        return False

def test_mysql_to_postgresql_conversion():
    """Test MySQL to PostgreSQL SQL conversion"""
    print("\n🔍 Testing MySQL to PostgreSQL Conversion...")
    
    try:
        from challenges.utils import convert_mysql_to_postgresql, _add_table_prefix_to_sql
        
        # Test MySQL schema conversion
        mysql_schema = """
        CREATE TABLE employees (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            salary DECIMAL(10,2),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        """
        
        pg_schema = convert_mysql_to_postgresql(mysql_schema)
        print("✅ MySQL to PostgreSQL conversion successful")
        print(f"Original: {mysql_schema.strip()}")
        print(f"Converted: {pg_schema.strip()}")
        
        # Test table prefix addition
        prefix = "challenge_test_abc123"
        prefixed_schema = _add_table_prefix_to_sql(pg_schema, prefix)
        print(f"✅ Table prefix addition successful")
        print(f"Prefixed: {prefixed_schema.strip()}")
        
        return True
        
    except Exception as e:
        print(f"❌ MySQL to PostgreSQL conversion failed: {e}")
        return False

def main():
    print("🚀 PostgreSQL Fix Test")
    print("="*60)
    
    # Load environment variables
    try:
        from dotenv import load_dotenv
        load_dotenv()
        print("✅ Environment variables loaded")
    except:
        print("⚠️  Could not load .env file")
    
    # Run tests
    pg_basic = test_postgresql_connection()
    django_pg = test_django_postgresql_connection()
    conversion = test_mysql_to_postgresql_conversion()
    
    print("\n" + "="*60)
    print("📊 TEST RESULTS")
    print("="*60)
    
    tests = [
        ("PostgreSQL Basic Connection", pg_basic),
        ("Django PostgreSQL Connection", django_pg),
        ("MySQL to PostgreSQL Conversion", conversion)
    ]
    
    passed = sum(1 for _, result in tests if result)
    total = len(tests)
    
    for test_name, result in tests:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL POSTGRESQL TESTS PASSED!")
        print("Both MySQL and PostgreSQL should now work in the challenge solve page.")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed.")
        print("Check the error messages above for details.")

if __name__ == "__main__":
    main()
