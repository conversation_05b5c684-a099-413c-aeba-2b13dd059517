#!/usr/bin/env python3
"""
Test Database Connections for cPanel Production
This script tests both MySQL and PostgreSQL connections using the current configuration.
"""

import os
import sys
import django
from django.conf import settings

# Add the project directory to Python path
project_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(project_dir)

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'sqlplayground.settings')
django.setup()

def test_mysql_connection():
    """Test MySQL connection using PyMySQL"""
    print("🔍 Testing MySQL Connection...")
    print(f"Host: {getattr(settings, 'MYSQL_HOST', 'Not set')}")
    print(f"Port: {getattr(settings, 'MYSQL_PORT', 'Not set')}")
    print(f"User: {getattr(settings, 'MYSQL_USER', 'Not set')}")
    print(f"Database: {getattr(settings, 'MYSQL_DB', 'Not set')}")
    
    try:
        # Try PyMySQL first
        import pymysql
        
        conn = pymysql.connect(
            host=getattr(settings, 'MYSQL_HOST', 'localhost'),
            port=getattr(settings, 'MYSQL_PORT', 3306),
            user=getattr(settings, 'MYSQL_USER', 'root'),
            password=getattr(settings, 'MYSQL_PASSWORD', ''),
            charset='utf8mb4'
        )
        
        cursor = conn.cursor()
        cursor.execute("SELECT VERSION()")
        version = cursor.fetchone()
        print(f"✅ MySQL Connection Successful! Version: {version[0]}")
        
        # Test database access
        try:
            cursor.execute(f"USE {getattr(settings, 'MYSQL_DB', 'sqlplayground_queries_mysql')}")
            print(f"✅ MySQL Database Access Successful!")
        except Exception as e:
            print(f"⚠️  MySQL Database Access Failed: {e}")
            
        cursor.close()
        conn.close()
        return True
        
    except ImportError:
        print("❌ PyMySQL not installed")
        return False
    except Exception as e:
        print(f"❌ MySQL Connection Failed: {e}")
        return False

def test_postgresql_connection():
    """Test PostgreSQL connection using pg8000"""
    print("\n🔍 Testing PostgreSQL Connection...")
    print(f"Host: {getattr(settings, 'POSTGRESQL_HOST', 'Not set')}")
    print(f"Port: {getattr(settings, 'POSTGRESQL_PORT', 'Not set')}")
    print(f"User: {getattr(settings, 'POSTGRESQL_USER', 'Not set')}")
    print(f"Database: {getattr(settings, 'POSTGRESQL_DB', 'Not set')}")
    
    try:
        # Try pg8000 first
        import pg8000
        
        conn = pg8000.connect(
            host=getattr(settings, 'POSTGRESQL_HOST', 'localhost'),
            port=getattr(settings, 'POSTGRESQL_PORT', 5432),
            user=getattr(settings, 'POSTGRESQL_USER', 'postgres'),
            password=getattr(settings, 'POSTGRESQL_PASSWORD', ''),
            database=getattr(settings, 'POSTGRESQL_DB', 'postgres')
        )
        
        cursor = conn.cursor()
        cursor.execute("SELECT version()")
        version = cursor.fetchone()
        print(f"✅ PostgreSQL Connection Successful! Version: {version[0]}")
        
        cursor.close()
        conn.close()
        return True
        
    except ImportError:
        print("❌ pg8000 not installed")
        return False
    except Exception as e:
        print(f"❌ PostgreSQL Connection Failed: {e}")
        return False

def test_django_database_connections():
    """Test Django database connections"""
    print("\n🔍 Testing Django Database Connections...")
    
    from django.db import connections
    
    # Test default database
    try:
        default_conn = connections['default']
        with default_conn.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
        print("✅ Django Default Database Connection Successful!")
    except Exception as e:
        print(f"❌ Django Default Database Connection Failed: {e}")
    
    # Test query_postgres database
    try:
        pg_conn = connections['query_postgres']
        with pg_conn.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
        print("✅ Django PostgreSQL Query Database Connection Successful!")
    except Exception as e:
        print(f"❌ Django PostgreSQL Query Database Connection Failed: {e}")
    
    # Test query_mysql database
    try:
        mysql_conn = connections['query_mysql']
        with mysql_conn.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
        print("✅ Django MySQL Query Database Connection Successful!")
    except Exception as e:
        print(f"❌ Django MySQL Query Database Connection Failed: {e}")

if __name__ == "__main__":
    print("🚀 Database Connection Test for cPanel Production")
    print("=" * 60)
    
    # Test environment variables
    print("\n📋 Environment Variables:")
    print(f"PRIMARY_DB_NAME: {os.environ.get('PRIMARY_DB_NAME', 'Not set')}")
    print(f"QUERY_POSTGRES_DB_NAME: {os.environ.get('QUERY_POSTGRES_DB_NAME', 'Not set')}")
    print(f"QUERY_MYSQL_DB_NAME: {os.environ.get('QUERY_MYSQL_DB_NAME', 'Not set')}")
    print(f"QUERY_POSTGRES_USER: {os.environ.get('QUERY_POSTGRES_USER', 'Not set')}")
    print(f"QUERY_MYSQL_USER: {os.environ.get('QUERY_MYSQL_USER', 'Not set')}")
    
    # Test direct connections
    mysql_ok = test_mysql_connection()
    pg_ok = test_postgresql_connection()
    
    # Test Django connections
    test_django_database_connections()
    
    print("\n" + "=" * 60)
    if mysql_ok and pg_ok:
        print("🎉 All database connections are working!")
    else:
        print("⚠️  Some database connections failed. Check the configuration.")
