#!/usr/bin/env python3
"""
Fix CSRF verification issues for Django application
This script diagnoses and fixes CSRF token problems
"""

import os
import sys

def setup_django():
    """Setup Django environment"""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'sqlplayground.settings')
    os.environ.setdefault('DEBUG', 'False')
    
    try:
        import django
        django.setup()
        print("✅ Django setup successful")
        return True
    except Exception as e:
        print(f"❌ Django setup failed: {e}")
        return False

def check_csrf_settings():
    """Check current CSRF settings"""
    print("🔍 Checking CSRF settings...")
    
    try:
        from django.conf import settings
        
        csrf_settings = {
            'CSRF_COOKIE_AGE': getattr(settings, 'CSRF_COOKIE_AGE', 'Not set'),
            'CSRF_COOKIE_HTTPONLY': getattr(settings, 'CSRF_COOKIE_HTTPONLY', 'Not set'),
            'CSRF_COOKIE_SAMESITE': getattr(settings, 'CSRF_COOKIE_SAMESITE', 'Not set'),
            'CSRF_COOKIE_SECURE': getattr(settings, 'CSRF_COOKIE_SECURE', 'Not set'),
            'CSRF_USE_SESSIONS': getattr(settings, 'CSRF_USE_SESSIONS', 'Not set'),
            'CSRF_TRUSTED_ORIGINS': getattr(settings, 'CSRF_TRUSTED_ORIGINS', 'Not set'),
            'DEBUG': getattr(settings, 'DEBUG', 'Not set'),
        }
        
        print("📋 Current CSRF Settings:")
        for key, value in csrf_settings.items():
            print(f"   {key}: {value}")
        
        # Check middleware
        middleware = getattr(settings, 'MIDDLEWARE', [])
        csrf_middleware = 'django.middleware.csrf.CsrfViewMiddleware'
        
        if csrf_middleware in middleware:
            print(f"✅ CSRF middleware is enabled")
            print(f"   Position: {middleware.index(csrf_middleware) + 1}/{len(middleware)}")
        else:
            print(f"❌ CSRF middleware is missing!")
        
        return True
        
    except Exception as e:
        print(f"❌ CSRF settings check failed: {e}")
        return False

def test_csrf_token_generation():
    """Test if CSRF tokens can be generated"""
    print("\n🧪 Testing CSRF token generation...")
    
    try:
        from django.middleware.csrf import get_token
        from django.test import RequestFactory
        
        # Create a test request
        factory = RequestFactory()
        request = factory.get('/')
        
        # Add session (required for CSRF)
        from django.contrib.sessions.middleware import SessionMiddleware
        middleware = SessionMiddleware(lambda req: None)
        middleware.process_request(request)
        request.session.save()
        
        # Generate CSRF token
        token = get_token(request)
        
        if token and len(token) > 10:
            print(f"✅ CSRF token generated successfully")
            print(f"   Token length: {len(token)}")
            print(f"   Token preview: {token[:10]}...")
            return True
        else:
            print(f"❌ CSRF token generation failed")
            return False
            
    except Exception as e:
        print(f"❌ CSRF token test failed: {e}")
        return False

def check_session_configuration():
    """Check session configuration (required for CSRF)"""
    print("\n🔍 Checking session configuration...")
    
    try:
        from django.conf import settings
        
        session_settings = {
            'SESSION_COOKIE_AGE': getattr(settings, 'SESSION_COOKIE_AGE', 'Not set'),
            'SESSION_SAVE_EVERY_REQUEST': getattr(settings, 'SESSION_SAVE_EVERY_REQUEST', 'Not set'),
            'SESSION_COOKIE_SECURE': getattr(settings, 'SESSION_COOKIE_SECURE', 'Not set'),
            'SESSION_COOKIE_SAMESITE': getattr(settings, 'SESSION_COOKIE_SAMESITE', 'Not set'),
        }
        
        print("📋 Session Settings:")
        for key, value in session_settings.items():
            print(f"   {key}: {value}")
        
        # Check session middleware
        middleware = getattr(settings, 'MIDDLEWARE', [])
        session_middleware = 'django.contrib.sessions.middleware.SessionMiddleware'
        
        if session_middleware in middleware:
            print(f"✅ Session middleware is enabled")
        else:
            print(f"❌ Session middleware is missing!")
        
        return True
        
    except Exception as e:
        print(f"❌ Session configuration check failed: {e}")
        return False

def create_csrf_test_view():
    """Create a test view to debug CSRF issues"""
    print("\n🔧 Creating CSRF test view...")
    
    test_view_content = '''
from django.shortcuts import render
from django.http import HttpResponse
from django.middleware.csrf import get_token
from django.views.decorators.csrf import csrf_exempt
import json

def csrf_test_view(request):
    """Test CSRF token functionality"""
    
    if request.method == 'POST':
        return HttpResponse(f"""
        <h1>✅ CSRF Test Successful!</h1>
        <p>POST request received successfully.</p>
        <p>CSRF token was valid.</p>
        <a href="/csrf-test/">← Back to test</a>
        """)
    
    # Generate CSRF token
    csrf_token = get_token(request)
    
    html = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>CSRF Test</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 40px; }}
            .form-group {{ margin: 20px 0; }}
            input, button {{ padding: 10px; margin: 5px; }}
            .info {{ background: #f0f0f0; padding: 15px; margin: 20px 0; }}
        </style>
    </head>
    <body>
        <h1>🔧 CSRF Token Test</h1>
        
        <div class="info">
            <h3>Debug Information:</h3>
            <p><strong>CSRF Token:</strong> {csrf_token}</p>
            <p><strong>Token Length:</strong> {len(csrf_token) if csrf_token else 0}</p>
            <p><strong>Session Key:</strong> {request.session.session_key}</p>
            <p><strong>User Agent:</strong> {request.META.get('HTTP_USER_AGENT', 'Unknown')}</p>
            <p><strong>Remote Address:</strong> {request.META.get('REMOTE_ADDR', 'Unknown')}</p>
        </div>
        
        <form method="post">
            <input type="hidden" name="csrfmiddlewaretoken" value="{csrf_token}">
            <div class="form-group">
                <label>Test Field:</label>
                <input type="text" name="test_field" value="test data" required>
            </div>
            <button type="submit">Submit Test</button>
        </form>
        
        <script>
        console.log('CSRF Token:', '{csrf_token}');
        console.log('Token Length:', {len(csrf_token) if csrf_token else 0});
        
        // Check if token is in form
        const tokenInput = document.querySelector('[name=csrfmiddlewaretoken]');
        if (tokenInput) {{
            console.log('✅ CSRF token found in form');
        }} else {{
            console.error('❌ CSRF token not found in form');
        }}
        </script>
    </body>
    </html>
    """
    
    return HttpResponse(html)
'''
    
    try:
        with open('csrf_test_view.py', 'w') as f:
            f.write(test_view_content)
        print("✅ CSRF test view created: csrf_test_view.py")
        return True
    except Exception as e:
        print(f"❌ Failed to create test view: {e}")
        return False

def update_csrf_settings():
    """Update CSRF settings for production"""
    print("\n🔧 Updating CSRF settings...")
    
    try:
        settings_file = 'sqlplayground/settings.py'
        
        # Read current settings
        with open(settings_file, 'r') as f:
            content = f.read()
        
        # Check if CSRF settings need updating
        csrf_updates = []
        
        # Update CSRF_COOKIE_SECURE for production
        if 'CSRF_COOKIE_SECURE = os.environ.get' not in content:
            csrf_updates.append("# CSRF should be secure in production but not in development")
            csrf_updates.append("CSRF_COOKIE_SECURE = not DEBUG  # Secure in production, not in development")
        
        # Update CSRF_COOKIE_SAMESITE
        if 'CSRF_COOKIE_SAMESITE' not in content or 'Lax' not in content:
            csrf_updates.append("CSRF_COOKIE_SAMESITE = 'Lax'  # Allow same-site requests")
        
        # Update CSRF_USE_SESSIONS
        if 'CSRF_USE_SESSIONS = False' not in content:
            csrf_updates.append("CSRF_USE_SESSIONS = False  # Use cookies, not sessions")
        
        if csrf_updates:
            # Add updates to settings
            csrf_section = "\n# CSRF Settings Update\n" + "\n".join(csrf_updates) + "\n"
            
            # Find a good place to insert (after existing CSRF settings)
            if 'CSRF_TRUSTED_ORIGINS' in content:
                content = content.replace(
                    'CSRF_TRUSTED_ORIGINS = os.environ.get',
                    csrf_section + '\nCSRF_TRUSTED_ORIGINS = os.environ.get'
                )
            else:
                # Add at the end of the file
                content += csrf_section
            
            # Write updated settings
            with open(settings_file, 'w') as f:
                f.write(content)
            
            print("✅ CSRF settings updated")
            return True
        else:
            print("✅ CSRF settings already optimal")
            return True
            
    except Exception as e:
        print(f"❌ Failed to update CSRF settings: {e}")
        return False

def main():
    """Main function to fix CSRF issues"""
    print("🚀 Fixing CSRF Verification Issues")
    print("=" * 40)
    
    # Setup Django
    if not setup_django():
        return
    
    # Check current CSRF settings
    check_csrf_settings()
    
    # Check session configuration
    check_session_configuration()
    
    # Test CSRF token generation
    test_csrf_token_generation()
    
    # Create test view
    create_csrf_test_view()
    
    # Update CSRF settings
    update_csrf_settings()
    
    print("\n🎉 CSRF Fix Complete!")
    print("\n📋 Next steps:")
    print("1. Restart your Python app in cPanel")
    print("2. Test signup at: https://kodesql.in/auth/register/")
    print("3. If still failing, test CSRF at: https://kodesql.in/csrf-test/")
    print("4. Check browser console for CSRF token issues")
    
    print("\n🔧 Common CSRF fixes:")
    print("- Clear browser cookies and cache")
    print("- Ensure JavaScript isn't blocking form submission")
    print("- Check that form has {% csrf_token %} tag")
    print("- Verify CSRF_TRUSTED_ORIGINS includes your domain")

if __name__ == "__main__":
    main()
