/* Enhanced styling for ChallengeTable inline admin with StackedInline */

/* Stacked inline styling for better visibility */
.inline-group .stacked .form-row {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    margin: 15px 0;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.inline-group .stacked .form-row:hover {
    background-color: #e9ecef;
    border-color: #417690;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

/* Enhanced textarea styling */
.inline-group textarea {
    min-height: 120px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 13px;
    line-height: 1.5;
    border-radius: 6px;
    border: 2px solid #dee2e6;
    padding: 12px;
    width: 100%;
    box-sizing: border-box;
}

.inline-group .field-schema_sql textarea {
    min-height: 150px;
    background-color: #f8f9fa;
    border-color: #6c757d;
}

.inline-group .field-run_dataset_sql textarea {
    min-height: 120px;
    background-color: #fff3cd;
    border-color: #ffc107;
}

.inline-group .field-submit_dataset_sql textarea {
    min-height: 120px;
    background-color: #d1ecf1;
    border-color: #17a2b8;
}

.inline-group .field-table_name input {
    font-weight: bold;
    background-color: #e9ecef;
    border: 2px solid #6c757d;
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 14px;
}

.inline-group .field-order input {
    width: 80px;
    text-align: center;
    font-weight: bold;
}

/* SUPER PROMINENT Add Another Database Button */
.add-row {
    margin: 30px 0;
    text-align: center;
    background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
    padding: 25px;
    border: 3px dashed #2196f3;
    border-radius: 12px;
    position: relative;
    animation: pulse-border 2s infinite;
}

@keyframes pulse-border {
    0% { border-color: #2196f3; }
    50% { border-color: #ff5722; }
    100% { border-color: #2196f3; }
}

.add-row:before {
    content: "🗄️ ADD ANOTHER DATABASE TABLE";
    display: block;
    font-size: 18px;
    font-weight: bold;
    color: #1976d2;
    margin-bottom: 10px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.add-row a {
    background: linear-gradient(135deg, #2196f3, #1976d2, #0d47a1);
    color: white;
    padding: 18px 36px;
    border-radius: 50px;
    text-decoration: none;
    font-weight: bold;
    font-size: 16px;
    display: inline-block;
    box-shadow: 0 6px 20px rgba(33, 150, 243, 0.3);
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    border: none;
    cursor: pointer;
}

.add-row a:before {
    content: "➕ ";
    font-size: 20px;
    margin-right: 10px;
}

.add-row a:hover {
    background: linear-gradient(135deg, #1976d2, #0d47a1, #01579b);
    color: white;
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 10px 30px rgba(33, 150, 243, 0.5);
}

.add-row a:after {
    content: "Click to add table schema, run dataset & submit dataset";
    font-size: 12px;
    opacity: 0.9;
    display: block;
    margin-top: 8px;
    font-weight: normal;
    text-transform: none;
    letter-spacing: normal;
}

/* Help text styling */
.inline-group .help {
    font-size: 11px;
    color: #666;
    margin-top: 2px;
}

/* Table headers */
.inline-group .tabular thead th {
    background-color: #f1f1f1;
    font-weight: bold;
    padding: 8px;
    border-bottom: 2px solid #ddd;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
    .inline-group .tabular textarea {
        min-height: 60px;
        font-size: 11px;
    }
}
