# Generated by Django 5.2.1 on 2025-07-06 12:03

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("challenges", "0004_xptransaction"),
    ]

    operations = [
        migrations.Remove<PERSON>ield(
            model_name="userchallengesubscription",
            name="stripe_payment_intent_id",
        ),
        migrations.AddField(
            model_name="userchallengesubscription",
            name="razorpay_order_id",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name="userchallengesubscription",
            name="razorpay_payment_id",
            field=models.Char<PERSON>ield(blank=True, max_length=255, null=True),
        ),
    ]
