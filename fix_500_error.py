#!/usr/bin/env python3
"""
Fix Django 500 Error on cPanel
This script diagnoses and fixes common causes of Django 500 errors
"""

import os
import sys
import django
from django.core.management import execute_from_command_line

def setup_django():
    """Setup Django environment"""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'sqlplayground.settings')
    django.setup()

def test_database_connections():
    """Test all database connections"""
    print("🔍 Testing Database Connections...")
    
    try:
        from django.db import connections
        
        # Test default database
        try:
            default_conn = connections['default']
            with default_conn.cursor() as cursor:
                cursor.execute("SELECT 1")
            print("   ✅ Default database connection working")
        except Exception as e:
            print(f"   ❌ Default database connection failed: {e}")
            return False
        
        # Test query databases
        for db_name in ['query_mysql', 'query_postgres']:
            try:
                if db_name in connections.databases:
                    conn = connections[db_name]
                    with conn.cursor() as cursor:
                        cursor.execute("SELECT 1")
                    print(f"   ✅ {db_name} database connection working")
                else:
                    print(f"   ⚠️  {db_name} not configured")
            except Exception as e:
                print(f"   ⚠️  {db_name} connection issue: {e}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Database connection test failed: {e}")
        return False

def check_essential_tables():
    """Check if essential Django tables exist"""
    print("🔍 Checking Essential Tables...")
    
    try:
        from django.db import connection
        
        essential_tables = [
            'django_migrations',
            'auth_user',
            'django_session',
            'django_site',
            'django_content_type'
        ]
        
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT table_name FROM information_schema.tables 
                WHERE table_schema = 'public'
            """)
            existing_tables = [row[0] for row in cursor.fetchall()]
        
        missing_tables = []
        for table in essential_tables:
            if table in existing_tables:
                print(f"   ✅ {table} exists")
            else:
                print(f"   ❌ {table} missing")
                missing_tables.append(table)
        
        if missing_tables:
            print(f"   ⚠️  Missing tables: {missing_tables}")
            return False
        
        print("   ✅ All essential tables present")
        return True
        
    except Exception as e:
        print(f"   ❌ Table check failed: {e}")
        return False

def test_django_models():
    """Test if Django models can be imported and used"""
    print("🔍 Testing Django Models...")
    
    try:
        # Test User model
        from django.contrib.auth.models import User
        user_count = User.objects.count()
        print(f"   ✅ User model working: {user_count} users")
        
        # Test Site model
        from django.contrib.sites.models import Site
        site_count = Site.objects.count()
        print(f"   ✅ Site model working: {site_count} sites")
        
        # Test ContentType model
        from django.contrib.contenttypes.models import ContentType
        ct_count = ContentType.objects.count()
        print(f"   ✅ ContentType model working: {ct_count} content types")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Model test failed: {e}")
        return False

def check_static_files():
    """Check static files configuration"""
    print("🔍 Checking Static Files...")
    
    try:
        from django.conf import settings
        
        static_root = getattr(settings, 'STATIC_ROOT', None)
        static_url = getattr(settings, 'STATIC_URL', None)
        
        print(f"   STATIC_URL: {static_url}")
        print(f"   STATIC_ROOT: {static_root}")
        
        if static_root and os.path.exists(static_root):
            print("   ✅ STATIC_ROOT directory exists")
        else:
            print("   ⚠️  STATIC_ROOT directory missing")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Static files check failed: {e}")
        return False

def run_collectstatic():
    """Run collectstatic to gather static files"""
    print("🔧 Running collectstatic...")
    
    try:
        execute_from_command_line(['manage.py', 'collectstatic', '--noinput'])
        print("   ✅ collectstatic completed successfully")
        return True
    except Exception as e:
        print(f"   ❌ collectstatic failed: {e}")
        return False

def create_superuser_if_needed():
    """Create a superuser if none exists"""
    print("🔍 Checking for Superuser...")
    
    try:
        from django.contrib.auth.models import User
        
        superusers = User.objects.filter(is_superuser=True)
        if superusers.exists():
            print(f"   ✅ {superusers.count()} superuser(s) exist")
            return True
        else:
            print("   ⚠️  No superuser found")
            print("   💡 You can create one with: python manage.py createsuperuser")
            return True
            
    except Exception as e:
        print(f"   ❌ Superuser check failed: {e}")
        return False

def test_url_patterns():
    """Test if URL patterns are working"""
    print("🔍 Testing URL Patterns...")
    
    try:
        from django.urls import reverse
        from django.test import Client
        
        # Test basic URLs
        test_urls = [
            ('admin:index', 'Admin'),
            # Add more URLs as needed
        ]
        
        for url_name, description in test_urls:
            try:
                url = reverse(url_name)
                print(f"   ✅ {description} URL: {url}")
            except Exception as e:
                print(f"   ⚠️  {description} URL issue: {e}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ URL pattern test failed: {e}")
        return False

def create_debug_view():
    """Create a simple debug view to test Django"""
    print("🔧 Creating Debug View...")
    
    debug_view_content = '''
from django.http import HttpResponse
from django.db import connections
import sys
import os

def debug_view(request):
    """Simple debug view to test Django functionality"""
    
    html = """
    <html>
    <head><title>Django Debug View</title></head>
    <body>
        <h1>Django Debug Information</h1>
        
        <h2>Django Status</h2>
        <p style="color: green;">✅ Django is running successfully!</p>
        
        <h2>Python Information</h2>
        <p>Python Version: {python_version}</p>
        <p>Django Version: {django_version}</p>
        
        <h2>Database Connections</h2>
        {db_status}
        
        <h2>Environment</h2>
        <p>DEBUG: {debug}</p>
        <p>ALLOWED_HOSTS: {allowed_hosts}</p>
        
        <h2>Next Steps</h2>
        <ul>
            <li><a href="/admin/">Access Admin Panel</a></li>
            <li><a href="/">Go to Homepage</a></li>
        </ul>
    </body>
    </html>
    """
    
    # Get Django version
    import django
    django_version = django.get_version()
    
    # Test database connections
    db_status = ""
    for db_name in connections:
        try:
            conn = connections[db_name]
            with conn.cursor() as cursor:
                cursor.execute("SELECT 1")
            db_status += f"<p style='color: green;'>✅ {db_name}: Connected</p>"
        except Exception as e:
            db_status += f"<p style='color: red;'>❌ {db_name}: {e}</p>"
    
    # Get settings
    from django.conf import settings
    debug = getattr(settings, 'DEBUG', 'Unknown')
    allowed_hosts = getattr(settings, 'ALLOWED_HOSTS', 'Unknown')
    
    return HttpResponse(html.format(
        python_version=sys.version,
        django_version=django_version,
        db_status=db_status,
        debug=debug,
        allowed_hosts=allowed_hosts
    ))
'''
    
    try:
        # Create debug_views.py
        with open('debug_views.py', 'w') as f:
            f.write(debug_view_content)
        
        print("   ✅ debug_views.py created")
        print("   💡 Add this to your URLs to test: path('debug/', debug_view)")
        return True
        
    except Exception as e:
        print(f"   ❌ Error creating debug view: {e}")
        return False

def main():
    """Main function"""
    print("🚀 Django 500 Error Fix for cPanel")
    print("="*50)
    
    # Setup Django
    try:
        setup_django()
        print("✅ Django setup successful")
    except Exception as e:
        print(f"❌ Django setup failed: {e}")
        print("💡 This might be the cause of your 500 error!")
        return
    
    # Run diagnostic tests
    tests = [
        ("Database Connections", test_database_connections),
        ("Essential Tables", check_essential_tables),
        ("Django Models", test_django_models),
        ("Static Files", check_static_files),
        ("Superuser Check", create_superuser_if_needed),
        ("URL Patterns", test_url_patterns)
    ]
    
    failed_tests = []
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}...")
        if not test_func():
            failed_tests.append(test_name)
    
    # Apply fixes
    print(f"\n🔧 Applying Fixes...")
    run_collectstatic()
    create_debug_view()
    
    print("\n" + "="*50)
    print("📋 DIAGNOSIS COMPLETE")
    print("="*50)
    
    if failed_tests:
        print(f"\n⚠️  Issues found: {len(failed_tests)}")
        for test in failed_tests:
            print(f"   - {test}")
        
        print("\n🔧 Recommended actions:")
        if "Database Connections" in failed_tests:
            print("   1. Run: python complete_migration_fix.py")
        if "Essential Tables" in failed_tests:
            print("   2. Run: python fix_cpanel_migrations.py")
        print("   3. Check cPanel error logs for detailed errors")
        print("   4. Ensure all environment variables are set correctly")
        
    else:
        print("\n✅ All tests passed!")
        print("The 500 error might be caused by:")
        print("   1. Missing static files (run collectstatic)")
        print("   2. URL configuration issues")
        print("   3. Template errors")
    
    print("\n💡 Next steps:")
    print("1. Check cPanel error logs for the exact error")
    print("2. Try accessing /admin/ to test basic Django functionality")
    print("3. Add the debug view to your URLs for testing")

if __name__ == "__main__":
    main()
