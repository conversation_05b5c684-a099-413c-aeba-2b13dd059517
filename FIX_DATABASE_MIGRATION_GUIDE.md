# Fix Database Migration Issue - users_userdatabase

## Problem
You're getting this error when logging in:
```
ProgrammingError: relation "users_userdatabase" does not exist
```

This means the `UserDatabase` model's table hasn't been created in your production PostgreSQL database.

## Root Cause
The Django migrations for the `UserDatabase` model haven't been applied to your production database. This commonly happens when:
1. Migrations were created locally but not run on production
2. Database was reset/recreated without running migrations
3. Migration files weren't uploaded to the server

## Quick Fix (Recommended)

### Step 1: Upload Migration Scripts
Upload these files to your cPanel file manager in the `/home/<USER>/public_html/KodeSQL/` directory:
- `fix_cpanel_database_migration.py`
- `run_cpanel_migrations.py`

### Step 2: Run the Fix Script
In your cPanel terminal or SSH, run:
```bash
cd /home/<USER>/public_html/KodeSQL
python fix_cpanel_database_migration.py
```

This script will:
- ✅ Check database connection
- ✅ Check if the table exists
- ✅ Create the table manually if missing
- ✅ Update Django migration state
- ✅ Test the UserDatabase model

### Step 3: Restart Python App
1. Go to cPanel → Python App
2. Find your KodeSQL application
3. Click "Restart"
4. Wait for green "Running" status

### Step 4: Test Login
Try logging in again at: https://kodesql.in/auth/login/

## Alternative Fix (Django Migrations)

If the quick fix doesn't work, try running Django migrations:

```bash
cd /home/<USER>/public_html/KodeSQL
python run_cpanel_migrations.py
```

This will:
- ✅ Run all pending Django migrations
- ✅ Show migration status
- ✅ Create missing tables
- ✅ Check for superuser

## Manual Fix (Last Resort)

If both scripts fail, you can create the table manually:

### 1. Access cPanel PostgreSQL
1. Go to cPanel → phpPgAdmin
2. Select your `sqlplayground_main` database
3. Go to SQL tab

### 2. Run This SQL
```sql
CREATE TABLE IF NOT EXISTS users_userdatabase (
    id BIGSERIAL PRIMARY KEY,
    database_path VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    last_accessed TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    user_id BIGINT NOT NULL UNIQUE REFERENCES users_user(id) ON DELETE CASCADE
);

CREATE INDEX IF NOT EXISTS users_userdatabase_user_id_idx 
ON users_userdatabase(user_id);

INSERT INTO django_migrations (app, name, applied) 
VALUES ('users', '0008_add_userdatabase', NOW())
ON CONFLICT (app, name) DO NOTHING;
```

### 3. Restart Python App
Restart your Python application in cPanel.

## What is UserDatabase?

The `UserDatabase` model tracks individual SQLite database files for each user. It's used for:
- 🗄️ User-specific SQL practice environments
- 📊 Tracking database usage
- 🔄 Managing user data isolation

## Verification

After applying the fix, you should be able to:
- ✅ Log in without errors
- ✅ Access the dashboard
- ✅ Use SQL challenges
- ✅ See user-specific data

## Prevention

To prevent this in the future:
1. Always run migrations after deploying code changes
2. Use the migration scripts provided
3. Check migration status regularly
4. Keep migration files in version control

## Troubleshooting

### If the fix script fails:
1. **Check database connection**: Verify your PostgreSQL credentials
2. **Check permissions**: Ensure database user has CREATE TABLE permissions
3. **Check logs**: Look at cPanel error logs for specific errors
4. **Try manual SQL**: Use the manual fix method above

### If login still fails after fix:
1. **Check other tables**: Run `python run_cpanel_migrations.py` to ensure all tables exist
2. **Check user model**: Verify the `users_user` table exists
3. **Check superuser**: Create a superuser if none exists
4. **Restart app**: Always restart the Python app after database changes

### Common Error Messages:
- `relation "users_user" does not exist` → Run full migrations
- `permission denied` → Check database user permissions
- `connection refused` → Check database connection settings

## Success Indicators

After the fix:
- ✅ Login works without database errors
- ✅ Dashboard loads correctly
- ✅ User profile features work
- ✅ SQL challenges are accessible
- ✅ Admin panel works (if you have superuser)

## Files Created

The fix creates these database objects:
- `users_userdatabase` table
- Index on `user_id` for performance
- Migration record in `django_migrations`

This ensures your Django application can properly track user-specific databases and provide personalized SQL learning environments.
