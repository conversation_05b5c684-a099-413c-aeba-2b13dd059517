#!/usr/bin/env python3
"""
Verify cPanel deployment configuration
Run this script after deploying to cPanel to verify everything is set up correctly
"""

import os
import sys

def check_htaccess():
    """Check .htaccess configuration"""
    print("🔧 Checking .htaccess Configuration...")
    
    if not os.path.exists('.htaccess'):
        print("❌ .htaccess file not found!")
        return False
    
    with open('.htaccess', 'r') as f:
        content = f.read()
    
    required_configs = [
        ('PassengerEnabled on', 'Passenger enabled'),
        ('PassengerAppRoot /home/<USER>/public_html/KodeSQL', 'Correct app root'),
        ('PassengerPython /home/<USER>/virtualenv/public_html/KodeSQL/3.10/bin/python', 'Correct Python path'),
        ('RewriteEngine On', 'URL rewriting enabled'),
        ('RewriteRule ^(.*)$ /passenger_wsgi.py [QSA,L]', 'Django routing rule'),
        ('passenger_wsgi.py', 'WSGI file referenced'),
    ]
    
    all_good = True
    for config, description in required_configs:
        if config in content:
            print(f"✅ {description}")
        else:
            print(f"❌ Missing: {description}")
            all_good = False
    
    return all_good

def check_passenger_wsgi():
    """Check passenger_wsgi.py file"""
    print("\n🐍 Checking passenger_wsgi.py...")
    
    if not os.path.exists('passenger_wsgi.py'):
        print("❌ passenger_wsgi.py file not found!")
        return False
    
    with open('passenger_wsgi.py', 'r') as f:
        content = f.read()
    
    required_elements = [
        ('sqlplayground.settings', 'Django settings module'),
        ('get_wsgi_application', 'WSGI application'),
        ('/home/<USER>/public_html/KodeSQL', 'Correct app path'),
        ('/home/<USER>/virtualenv/public_html/KodeSQL/3.10/lib/python3.10/site-packages', 'Virtual environment path'),
    ]
    
    all_good = True
    for element, description in required_elements:
        if element in content:
            print(f"✅ {description}")
        else:
            print(f"❌ Missing: {description}")
            all_good = False
    
    return all_good

def check_static_files():
    """Check static files configuration"""
    print("\n📁 Checking Static Files...")
    
    # Check if staticfiles directory exists
    if os.path.exists('staticfiles'):
        print("✅ staticfiles directory exists")
        
        # Check for key static files
        key_files = [
            'staticfiles/admin/css/base.css',
            'staticfiles/css',
            'staticfiles/js',
        ]
        
        for file_path in key_files:
            if os.path.exists(file_path):
                print(f"✅ {file_path}")
            else:
                print(f"⚠️  {file_path} not found")
    else:
        print("❌ staticfiles directory not found")
        print("   Run: python manage.py collectstatic --noinput")
        return False
    
    return True

def check_settings_file():
    """Check Django settings"""
    print("\n⚙️  Checking Django Settings...")
    
    try:
        # Add current directory to path
        sys.path.insert(0, os.getcwd())
        
        # Import settings without Django setup
        import importlib.util
        spec = importlib.util.spec_from_file_location("settings", "sqlplayground/settings.py")
        settings = importlib.util.module_from_spec(spec)
        
        # Check key settings
        print("✅ Settings file can be imported")
        
        return True
        
    except Exception as e:
        print(f"❌ Settings import failed: {e}")
        return False

def check_url_files():
    """Check URL configuration files"""
    print("\n🔗 Checking URL Configuration...")
    
    url_files = [
        'sqlplayground/urls.py',
        'core/urls.py',
        'users/urls.py',
        'challenges/urls.py',
        'tutorials/urls.py',
        'courses/urls.py',
        'editor/urls.py',
    ]
    
    all_good = True
    for url_file in url_files:
        if os.path.exists(url_file):
            print(f"✅ {url_file}")
        else:
            print(f"❌ {url_file} not found")
            all_good = False
    
    return all_good

def main():
    """Main verification function"""
    print("🚀 cPanel Deployment Verification")
    print("=" * 40)
    
    checks = [
        (".htaccess Configuration", check_htaccess),
        ("passenger_wsgi.py", check_passenger_wsgi),
        ("Static Files", check_static_files),
        ("Django Settings", check_settings_file),
        ("URL Configuration", check_url_files),
    ]
    
    results = []
    for name, check_func in checks:
        print(f"\n--- {name} ---")
        try:
            result = check_func()
            results.append((name, result))
        except Exception as e:
            print(f"❌ {name} failed: {e}")
            results.append((name, False))
    
    # Summary
    print("\n" + "=" * 40)
    print("📋 VERIFICATION RESULTS:")
    
    all_passed = True
    for name, result in results:
        status = "✅" if result else "❌"
        print(f"{status} {name}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 Deployment verification successful!")
        print("\n🔧 Final Steps:")
        print("1. Upload files to cPanel")
        print("2. Restart Python application in cPanel")
        print("3. Run: python manage.py collectstatic --noinput")
        print("4. Test URLs:")
        print("   - https://kodesql.in/")
        print("   - https://kodesql.in/dashboard/")
        print("   - https://kodesql.in/auth/login/")
        print("   - https://kodesql.in/debug-routing/")
    else:
        print("\n⚠️  Some checks failed. Please fix the issues above.")
        print("\n🔧 Common fixes:")
        print("- Ensure all files are uploaded to the correct directory")
        print("- Check file permissions (644 for most files)")
        print("- Verify cPanel Python app configuration")
        print("- Run collectstatic command")

if __name__ == "__main__":
    main()
