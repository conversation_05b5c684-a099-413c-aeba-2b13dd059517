# cPanel Database Connection Fix Guide

This guide addresses the specific database connection issues you're experiencing on cPanel production.

## Issues Identified

### 1. MySQL Error: "No module named 'mysql'"
**Problem**: The code was trying to import `mysql.connector` but cPanel uses PyMySQL.
**Solution**: Updated all MySQL connection code to use PyMySQL first, with fallback to mysql.connector.

### 2. PostgreSQL Error: Authentication failure
**Problem**: User mismatch - error shows `kodesqli_main_database2` but .env shows `kodesqli_postgres`.
**Solution**: Updated PostgreSQL connection code and created cPanel-specific configuration.

## Files Modified

### 1. `sqlplayground/settings.py`
- Changed from `django_pg8000` to `django.db.backends.postgresql` for better cPanel compatibility
- Updated database configuration to use standard PostgreSQL backend

### 2. `challenges/utils.py`
- Updated `_get_mysql_connection()` to use PyMySQL first
- Updated `_get_postgresql_connection()` to use psycopg2 first
- Updated `_execute_mysql_dual_dataset()` to use PyMySQL
- Updated `_execute_mysql_schema_query()` to use PyMySQL
- Updated `_execute_postgresql_dual_dataset()` to use psycopg2 first

### 3. `cpanel_database_config.py` (NEW)
- Created cPanel-specific database configuration helper
- Provides optimized connection settings for cPanel hosting

## Deployment Steps for cPanel

### Step 1: Update Environment Variables
Check your `.env` file on cPanel and ensure these variables match your actual database setup:

```bash
# PostgreSQL Settings
QUERY_POSTGRES_DB_NAME=kodesqli_queries_pg
QUERY_POSTGRES_USER=kodesqli_postgres
QUERY_POSTGRES_PASSWORD=forgex99

# MySQL Settings  
QUERY_MYSQL_DB_NAME=kodesqli_queries_mysql
QUERY_MYSQL_USER=kodesqli_mysql
QUERY_MYSQL_PASSWORD=forgex99
```

### Step 2: Verify Database Names in cPanel
1. Log into your cPanel
2. Go to "MySQL Databases" and "PostgreSQL Databases"
3. Verify the actual database names and usernames
4. Update your `.env` file if they don't match

### Step 3: Upload Modified Files
Upload these modified files to your cPanel:
- `sqlplayground/settings.py`
- `challenges/utils.py`
- `cpanel_database_config.py` (new file)

### Step 4: Install Required Packages
Ensure these packages are installed in your cPanel Python environment:
```bash
pip install PyMySQL psycopg2
```

### Step 5: Test Database Connections
Create a simple test script on cPanel to verify connections:

```python
# test_cpanel_db.py
import os
import pymysql
import psycopg2

# Test MySQL
try:
    mysql_conn = pymysql.connect(
        host='localhost',
        user='kodesqli_mysql',
        password='forgex99',
        database='kodesqli_queries_mysql'
    )
    print("✅ MySQL connection successful")
    mysql_conn.close()
except Exception as e:
    print(f"❌ MySQL failed: {e}")

# Test PostgreSQL
try:
    pg_conn = psycopg2.connect(
        host='localhost',
        user='kodesqli_postgres',
        password='forgex99',
        database='kodesqli_queries_pg'
    )
    print("✅ PostgreSQL connection successful")
    pg_conn.close()
except Exception as e:
    print(f"❌ PostgreSQL failed: {e}")
```

## Common cPanel Database Issues & Solutions

### Issue 1: Database User Permissions
**Problem**: User doesn't have access to database
**Solution**: In cPanel, go to "MySQL Databases" → "Add User to Database" and grant ALL PRIVILEGES

### Issue 2: Database Name Prefix
**Problem**: cPanel adds username prefix to database names
**Solution**: Use full database name including prefix (e.g., `kodesqli_queries_mysql`)

### Issue 3: Host Connection Issues
**Problem**: Connection refused or timeout
**Solution**: Use `localhost` as host, ensure databases are created in same cPanel account

### Issue 4: SSL/TLS Issues
**Problem**: SSL connection errors
**Solution**: Add `'sslmode': 'disable'` to PostgreSQL OPTIONS in settings.py if needed

## Verification Steps

After deployment, test the challenge solve page:

1. Go to any challenge page
2. Try running a simple query like `SELECT 1`
3. Switch between MySQL and PostgreSQL engines
4. Check for any error messages

## Troubleshooting

If you still get errors:

1. **Check cPanel Error Logs**: Look in cPanel → "Error Logs" for detailed error messages
2. **Verify Database Credentials**: Double-check usernames, passwords, and database names
3. **Test Direct Connections**: Use the test script above to verify basic connectivity
4. **Check Python Packages**: Ensure PyMySQL and psycopg2 are installed

## Next Steps

After fixing the database connections:
1. Test challenge execution thoroughly
2. Verify both MySQL and PostgreSQL engines work
3. Check that fallback from MySQL to PostgreSQL works correctly
4. Monitor for any remaining connection issues

The fixes prioritize PyMySQL and psycopg2 which are the most compatible drivers for cPanel hosting environments.
