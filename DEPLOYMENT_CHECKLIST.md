# cPanel Django Deployment Checklist

## ✅ Pre-Deployment Verification
All configurations have been verified and are ready for deployment:

- ✅ .htaccess configuration with proper URL routing
- ✅ passenger_wsgi.py with correct paths and error handling
- ✅ Django settings with production domain in ALLOWED_HOSTS
- ✅ URL patterns properly configured
- ✅ Static files collected and ready
- ✅ WhiteNoise configured for production static file serving

## 🚀 Deployment Steps

### Step 1: Upload Updated Files
Upload these **specific files** to your cPanel file manager:

**Critical Files to Upload:**
```
📁 /home/<USER>/public_html/KodeSQL/
├── .htaccess (UPDATED - contains new routing rules)
├── passenger_wsgi.py (UPDATED - clean encoding)
├── sqlplayground/settings.py (UPDATED - production domains)
├── sqlplayground/urls.py (UPDATED - optimized file serving)
└── staticfiles/ (entire directory)
```

### Step 2: Set File Permissions
Ensure correct permissions:
```bash
chmod 644 .htaccess
chmod 644 passenger_wsgi.py
chmod 755 sqlplayground/
chmod 644 sqlplayground/*.py
```

### Step 3: Restart Python Application
1. Login to cPanel
2. Go to **Python App** section
3. Find your **KodeSQL** application
4. Click **"Restart"** button
5. Wait for green "Running" status

### Step 4: Collect Static Files (if needed)
If static files are missing, run:
```bash
cd /home/<USER>/public_html/KodeSQL
python manage.py collectstatic --noinput
```

### Step 5: Test URLs
Test these URLs in order:

1. **Debug URL First**: https://kodesql.in/debug-routing/
   - If this works, Django routing is functioning
   - If this fails, check cPanel error logs

2. **Core URLs**:
   - ✅ https://kodesql.in/ (landing page)
   - ✅ https://kodesql.in/dashboard/ (dashboard)
   - ✅ https://kodesql.in/about/ (about page)

3. **Authentication URLs**:
   - ✅ https://kodesql.in/auth/login/ (login)
   - ✅ https://kodesql.in/auth/register/ (register)

4. **App URLs**:
   - ✅ https://kodesql.in/challenges/ (challenges)
   - ✅ https://kodesql.in/tutorials/ (tutorials)
   - ✅ https://kodesql.in/courses/ (courses)
   - ✅ https://kodesql.in/admin/ (admin panel)

## 🔧 What Was Fixed

### Problem: 404 Errors for All Pages Except Landing Page
**Root Cause**: Insufficient .htaccess configuration

### Solution: Django-Native URL Routing
1. **Enhanced .htaccess**: Added proper URL rewriting rules
2. **Request Routing**: All requests now go through Django
3. **Static File Handling**: Optimized for production environment
4. **Error Handling**: Custom error pages through Django

### Key Changes:
```apache
# NEW: Route all requests through Django
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} !^/static/
RewriteCond %{REQUEST_URI} !^/media/
RewriteRule ^(.*)$ /passenger_wsgi.py [QSA,L]
```

## 🚨 Troubleshooting

### If 404 Errors Still Occur:

1. **Check Debug URL**: https://kodesql.in/debug-routing/
   - Shows if Django is receiving requests
   - Displays environment information

2. **Check cPanel Error Logs**:
   - cPanel → Error Logs
   - Look for Python/Django errors

3. **Verify Python App Settings**:
   - App Root: `/home/<USER>/public_html/KodeSQL`
   - Python Path: `/home/<USER>/virtualenv/public_html/KodeSQL/3.10/bin/python`
   - Startup File: `passenger_wsgi.py`

4. **Alternative .htaccess** (last resort):
   ```apache
   PassengerEnabled on
   PassengerAppRoot /home/<USER>/public_html/KodeSQL
   PassengerPython /home/<USER>/virtualenv/public_html/KodeSQL/3.10/bin/python
   ```

### Common Issues:
- **File permissions**: Ensure 644 for .htaccess and .py files
- **Python app not running**: Restart in cPanel
- **Static files missing**: Run collectstatic command
- **Wrong paths**: Verify cPanel Python app configuration

## ✅ Success Indicators

After deployment, you should see:
- ✅ Landing page loads correctly
- ✅ All internal pages work (no 404 errors)
- ✅ CSS and JavaScript load properly
- ✅ Admin panel accessible
- ✅ User authentication works
- ✅ Debug page shows Django is working

## 📞 Support

If issues persist after following this checklist:
1. Check cPanel error logs for specific errors
2. Verify all file paths in cPanel Python app settings
3. Test the debug URL to confirm Django is receiving requests
4. Ensure virtual environment is properly activated

The solution uses **Django-native approaches** rather than complex Apache configurations, making it more reliable and maintainable for cPanel hosting.
