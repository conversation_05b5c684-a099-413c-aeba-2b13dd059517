#!/usr/bin/env python3
"""
Test the fixes applied for Django 404 issues
"""

import os
import sys
import django

def setup_django():
    """Setup Django environment"""
    sys.path.insert(0, os.getcwd())
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'sqlplayground.settings')
    django.setup()

def test_url_debug_view():
    """Test if the URL debug view works now"""
    print("🔧 Testing URL Debug View...")
    
    try:
        from django.test import Client
        
        client = Client()
        response = client.get('/url-debug/')
        
        if response.status_code == 200:
            print("✅ URL debug view working - status 200")
            return True
        else:
            print(f"❌ URL debug view failed - status {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ URL debug view error: {e}")
        return False

def test_all_main_urls():
    """Test all main URLs"""
    print("\n🔧 Testing All Main URLs...")
    
    try:
        from django.test import Client
        
        client = Client()
        
        test_urls = [
            ('/', 'Landing Page'),
            ('/dashboard/', 'Dashboard'),
            ('/challenges/', 'Challenges'),
            ('/auth/login/', 'Login'),
            ('/auth/register/', 'Register'),
            ('/url-debug/', 'URL Debug'),
            ('/debug-info/', 'Debug Info'),
        ]
        
        all_working = True
        
        for url, name in test_urls:
            try:
                response = client.get(url)
                if response.status_code in [200, 302]:
                    print(f"✅ {url} ({name}) → {response.status_code}")
                else:
                    print(f"❌ {url} ({name}) → {response.status_code}")
                    all_working = False
            except Exception as e:
                print(f"❌ {url} ({name}) → ERROR: {e}")
                all_working = False
        
        return all_working
        
    except Exception as e:
        print(f"❌ URL testing failed: {e}")
        return False

def check_imports_fixed():
    """Check if the import issues are fixed"""
    print("\n🔧 Checking Import Fixes...")
    
    try:
        # Test core views import
        from core.views import url_debug_view
        print("✅ core.views.url_debug_view imported successfully")
        
        # Test allauth import fix
        from users.adapters import DefaultSocialAccountAdapter
        print("✅ users.adapters imports working")
        
        from users.views import login_view
        print("✅ users.views imports working")
        
        return True
        
    except Exception as e:
        print(f"❌ Import check failed: {e}")
        return False

def generate_test_report():
    """Generate a test report"""
    print("\n🔧 Generating Test Report...")
    
    try:
        from django.urls import reverse
        
        # Test URL reverse generation
        test_names = [
            'core:landing_page',
            'core:dashboard',
            'core:url_debug',
            'challenges:challenges_list',
            'users:login',
            'users:register',
        ]
        
        report = []
        for name in test_names:
            try:
                url = reverse(name)
                report.append(f"✅ {name} → {url}")
            except Exception as e:
                report.append(f"❌ {name} → ERROR: {e}")
        
        # Write report to file
        with open('url_test_report.txt', 'w') as f:
            f.write("KodeSQL URL Test Report\n")
            f.write("=" * 30 + "\n\n")
            f.write("URL Reverse Generation Test:\n")
            for line in report:
                f.write(line + "\n")
            f.write("\nGenerated by: test_fixes_applied.py\n")
        
        print("✅ Test report saved to url_test_report.txt")
        return True
        
    except Exception as e:
        print(f"❌ Report generation failed: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Testing Applied Fixes")
    print("=" * 40)
    
    # Setup Django
    try:
        setup_django()
        print("✅ Django setup successful")
    except Exception as e:
        print(f"❌ Django setup failed: {e}")
        return
    
    # Run tests
    tests = [
        ("Import Fixes", check_imports_fixed),
        ("URL Debug View", test_url_debug_view),
        ("All Main URLs", test_all_main_urls),
        ("Generate Report", generate_test_report),
    ]
    
    results = []
    for name, test_func in tests:
        print(f"\n--- {name} ---")
        try:
            result = test_func()
            results.append((name, result))
        except Exception as e:
            print(f"❌ {name} failed: {e}")
            results.append((name, False))
    
    # Summary
    print("\n" + "=" * 40)
    print("📋 TEST RESULTS:")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for name, result in results:
        status = "✅" if result else "❌"
        print(f"{status} {name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All fixes applied successfully!")
        print("\n📋 Next Steps:")
        print("1. Test URL debug page: https://kodesql.in/url-debug/")
        print("2. Check browser developer tools for any remaining 404s")
        print("3. Look for template links missing trailing slashes")
        print("4. Clear browser cache and test navigation")
    else:
        print("\n⚠️  Some issues remain. Check the failed tests above.")
    
    print("\n💡 Remember:")
    print("- The diagnostic showed most URLs are working (200/302)")
    print("- 404 errors are likely from template links or JavaScript")
    print("- Use browser dev tools to see exact URLs being requested")

if __name__ == "__main__":
    main()
