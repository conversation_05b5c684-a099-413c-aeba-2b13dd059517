#!/usr/bin/env python3
"""
Diagnose Django 404 errors - URLs showing Django's custom 404 page
This means Django is receiving requests but can't match URLs
"""

import os
import sys
import django

def setup_django():
    """Setup Django environment"""
    sys.path.insert(0, os.getcwd())
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'sqlplayground.settings')
    django.setup()

def test_url_resolution():
    """Test URL resolution for common paths"""
    print("🔧 Testing URL Resolution...")
    
    try:
        from django.urls import resolve, reverse
        from django.conf import settings
        
        print(f"DEBUG mode: {settings.DEBUG}")
        print(f"ROOT_URLCONF: {settings.ROOT_URLCONF}")
        
        # Test URLs that should work
        test_paths = [
            '/',
            '/dashboard/',
            '/challenges/',
            '/auth/login/',
            '/auth/register/',
            '/admin/',
            '/about/',
            '/debug-routing/',
        ]
        
        print("\n📋 URL Resolution Test:")
        for path in test_paths:
            try:
                match = resolve(path)
                print(f"✅ {path} → {match.view_name} ({match.func.__module__}.{match.func.__name__})")
            except Exception as e:
                print(f"❌ {path} → ERROR: {e}")
        
        return True
    except Exception as e:
        print(f"❌ URL resolution test failed: {e}")
        return False

def test_reverse_urls():
    """Test reverse URL generation"""
    print("\n🔧 Testing Reverse URL Generation...")
    
    try:
        from django.urls import reverse
        
        # Test reverse URL generation
        test_names = [
            ('core:landing_page', '/'),
            ('core:dashboard', '/dashboard/'),
            ('core:about', '/about/'),
            ('challenges:challenges_list', '/challenges/'),
            ('users:login', '/auth/login/'),
            ('users:register', '/auth/register/'),
            ('admin:index', '/admin/'),
        ]
        
        print("\n📋 Reverse URL Test:")
        for name, expected in test_names:
            try:
                url = reverse(name)
                status = "✅" if url == expected else f"⚠️  Expected {expected}, got {url}"
                print(f"{status} {name} → {url}")
            except Exception as e:
                print(f"❌ {name} → ERROR: {e}")
        
        return True
    except Exception as e:
        print(f"❌ Reverse URL test failed: {e}")
        return False

def check_installed_apps():
    """Check if all apps are properly installed"""
    print("\n🔧 Checking Installed Apps...")
    
    try:
        from django.conf import settings
        from django.apps import apps
        
        required_apps = [
            'core',
            'users', 
            'challenges',
            'courses',
            'tutorials',
            'editor',
        ]
        
        print("\n📋 App Installation Check:")
        for app_name in required_apps:
            if app_name in settings.INSTALLED_APPS:
                try:
                    app_config = apps.get_app_config(app_name)
                    print(f"✅ {app_name} → Installed and loaded")
                except Exception as e:
                    print(f"❌ {app_name} → Installed but error: {e}")
            else:
                print(f"❌ {app_name} → Not in INSTALLED_APPS")
        
        return True
    except Exception as e:
        print(f"❌ App check failed: {e}")
        return False

def test_view_imports():
    """Test if views can be imported"""
    print("\n🔧 Testing View Imports...")
    
    try:
        # Test core views
        from core import views as core_views
        print("✅ core.views imported successfully")
        
        # Test users views
        from users import views as users_views
        print("✅ users.views imported successfully")
        
        # Test challenges views
        from challenges import views as challenges_views
        print("✅ challenges.views imported successfully")
        
        # Test specific view functions
        test_views = [
            (core_views, 'landing_page'),
            (core_views, 'dashboard'),
            (users_views, 'login_view'),
            (challenges_views, 'challenges_list'),
        ]
        
        print("\n📋 View Function Check:")
        for module, view_name in test_views:
            if hasattr(module, view_name):
                print(f"✅ {module.__name__}.{view_name} exists")
            else:
                print(f"❌ {module.__name__}.{view_name} missing")
        
        return True
    except Exception as e:
        print(f"❌ View import test failed: {e}")
        return False

def check_template_existence():
    """Check if templates exist"""
    print("\n🔧 Checking Template Existence...")
    
    try:
        from django.template.loader import get_template
        
        # Test key templates
        test_templates = [
            'core/landing_page.html',
            'core/dashboard.html',
            'users/login.html',
            'challenges/challenges_list.html',
            '404.html',
            '500.html',
        ]
        
        print("\n📋 Template Check:")
        for template_name in test_templates:
            try:
                template = get_template(template_name)
                print(f"✅ {template_name} → Found")
            except Exception as e:
                print(f"❌ {template_name} → ERROR: {e}")
        
        return True
    except Exception as e:
        print(f"❌ Template check failed: {e}")
        return False

def test_database_connection():
    """Test database connection"""
    print("\n🔧 Testing Database Connection...")
    
    try:
        from django.db import connection
        
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
        
        print("✅ Database connection successful")
        return True
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

def create_url_debug_view():
    """Create a debug view to test URL routing"""
    print("\n🔧 Creating URL Debug View...")
    
    debug_view_code = '''
from django.http import HttpResponse
from django.urls import reverse, resolve
from django.conf import settings
import json

def url_debug_view(request):
    """Debug view to show URL routing information"""
    
    # Get current request info
    current_path = request.path
    current_method = request.method
    
    # Test URL resolution
    url_tests = []
    test_paths = ['/', '/dashboard/', '/challenges/', '/auth/login/']
    
    for path in test_paths:
        try:
            match = resolve(path)
            url_tests.append({
                'path': path,
                'status': 'OK',
                'view_name': match.view_name,
                'view_func': f"{match.func.__module__}.{match.func.__name__}"
            })
        except Exception as e:
            url_tests.append({
                'path': path,
                'status': 'ERROR',
                'error': str(e)
            })
    
    # Test reverse URLs
    reverse_tests = []
    test_names = [
        'core:landing_page',
        'core:dashboard', 
        'challenges:challenges_list',
        'users:login'
    ]
    
    for name in test_names:
        try:
            url = reverse(name)
            reverse_tests.append({
                'name': name,
                'status': 'OK',
                'url': url
            })
        except Exception as e:
            reverse_tests.append({
                'name': name,
                'status': 'ERROR',
                'error': str(e)
            })
    
    html = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>URL Debug Information</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 20px; }}
            .success {{ color: green; }}
            .error {{ color: red; }}
            .info {{ background: #f0f0f0; padding: 10px; margin: 10px 0; }}
            table {{ border-collapse: collapse; width: 100%; }}
            th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
            th {{ background-color: #f2f2f2; }}
        </style>
    </head>
    <body>
        <h1>🐛 URL Debug Information</h1>
        
        <div class="info">
            <h2>Current Request</h2>
            <p><strong>Path:</strong> {current_path}</p>
            <p><strong>Method:</strong> {current_method}</p>
            <p><strong>DEBUG:</strong> {settings.DEBUG}</p>
            <p><strong>ROOT_URLCONF:</strong> {settings.ROOT_URLCONF}</p>
        </div>
        
        <h2>URL Resolution Tests</h2>
        <table>
            <tr><th>Path</th><th>Status</th><th>View Name</th><th>View Function</th></tr>
            {"".join([
                f"<tr><td>{test['path']}</td><td class='{'success' if test['status'] == 'OK' else 'error'}'>{test['status']}</td><td>{test.get('view_name', '')}</td><td>{test.get('view_func', test.get('error', ''))}</td></tr>"
                for test in url_tests
            ])}
        </table>
        
        <h2>Reverse URL Tests</h2>
        <table>
            <tr><th>URL Name</th><th>Status</th><th>Generated URL</th></tr>
            {"".join([
                f"<tr><td>{test['name']}</td><td class='{'success' if test['status'] == 'OK' else 'error'}'>{test['status']}</td><td>{test.get('url', test.get('error', ''))}</td></tr>"
                for test in reverse_tests
            ])}
        </table>
        
        <div class="info">
            <h2>Next Steps</h2>
            <p>If URLs show errors above, the issue is with Django URL configuration.</p>
            <p>If URLs work here but fail on actual pages, the issue is with template links or form actions.</p>
        </div>
    </body>
    </html>
    """
    
    return HttpResponse(html)
'''
    
    try:
        with open('url_debug_view.py', 'w') as f:
            f.write(debug_view_code)
        print("✅ Created url_debug_view.py")
        print("💡 Add this to core/views.py and create URL pattern")
        return True
    except Exception as e:
        print(f"❌ Failed to create debug view: {e}")
        return False

def main():
    """Main diagnostic function"""
    print("🚀 Diagnosing Django 404 Errors")
    print("=" * 50)
    
    # Setup Django
    try:
        setup_django()
        print("✅ Django setup successful")
    except Exception as e:
        print(f"❌ Django setup failed: {e}")
        return
    
    # Run diagnostics
    diagnostics = [
        ("URL Resolution", test_url_resolution),
        ("Reverse URLs", test_reverse_urls),
        ("Installed Apps", check_installed_apps),
        ("View Imports", test_view_imports),
        ("Template Existence", check_template_existence),
        ("Database Connection", test_database_connection),
        ("Create Debug View", create_url_debug_view),
    ]
    
    results = []
    for name, diagnostic_func in diagnostics:
        print(f"\n--- {name} ---")
        try:
            result = diagnostic_func()
            results.append((name, result))
        except Exception as e:
            print(f"❌ {name} failed: {e}")
            results.append((name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 DIAGNOSTIC RESULTS:")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for name, result in results:
        status = "✅" if result else "❌"
        print(f"{status} {name}")
    
    print(f"\nOverall: {passed}/{total} diagnostics passed")
    
    if passed >= total - 1:
        print("\n🤔 Django configuration looks good!")
        print("The 404 errors might be caused by:")
        print("1. Template links using wrong URLs")
        print("2. Form actions pointing to wrong paths") 
        print("3. JavaScript making requests to wrong endpoints")
        print("4. Static file conflicts")
        print("\n💡 Check the actual HTML source of your pages")
    else:
        print("\n❌ Found Django configuration issues!")
        print("Fix the failed diagnostics above")

if __name__ == "__main__":
    main()
