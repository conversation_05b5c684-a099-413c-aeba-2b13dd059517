#!/usr/bin/env python3
"""
Test URL routing for Django application
This script tests if all URL patterns are working correctly
"""

import os
import sys
import django

def setup_django():
    """Setup Django environment"""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'sqlplayground.settings')
    django.setup()

def test_url_patterns():
    """Test all URL patterns"""
    print("🔍 Testing URL Patterns...")
    
    try:
        from django.urls import reverse
        from django.conf import settings
        
        # Test core URLs
        test_urls = [
            ('core:landing_page', '/'),
            ('core:dashboard', '/dashboard/'),
            ('core:about', '/about/'),
            ('core:home', '/editor/'),
            ('users:login', '/auth/login/'),
            ('users:register', '/auth/register/'),
            ('admin:index', '/admin/'),
            ('challenges:challenge_list', '/challenges/'),
            ('tutorials:tutorial_list', '/tutorials/'),
            ('courses:course_list', '/courses/'),
        ]
        
        print(f"DEBUG mode: {settings.DEBUG}")
        print(f"ROOT_URLCONF: {settings.ROOT_URLCONF}")
        print(f"ALLOWED_HOSTS: {settings.ALLOWED_HOSTS}")
        
        success_count = 0
        total_count = len(test_urls)
        
        for name, expected_path in test_urls:
            try:
                url = reverse(name)
                if url == expected_path:
                    print(f"✅ {name}: {url}")
                    success_count += 1
                else:
                    print(f"⚠️  {name}: Expected {expected_path}, got {url}")
            except Exception as e:
                print(f"❌ {name}: {e}")
        
        print(f"\n📊 Results: {success_count}/{total_count} URLs working correctly")
        return success_count == total_count
        
    except Exception as e:
        print(f"❌ URL patterns test failed: {e}")
        return False

def test_static_files():
    """Test static files configuration"""
    print("\n📁 Testing Static Files Configuration...")
    
    try:
        from django.conf import settings
        from django.contrib.staticfiles import finders
        
        print(f"STATIC_URL: {settings.STATIC_URL}")
        print(f"STATIC_ROOT: {settings.STATIC_ROOT}")
        print(f"STATICFILES_DIRS: {settings.STATICFILES_DIRS}")
        print(f"DEBUG: {settings.DEBUG}")
        
        # Check if static files exist
        if hasattr(settings, 'STATIC_ROOT') and settings.STATIC_ROOT:
            if os.path.exists(str(settings.STATIC_ROOT)):
                print("✅ STATIC_ROOT directory exists")
            else:
                print("⚠️  STATIC_ROOT directory doesn't exist")
                print("   Run: python manage.py collectstatic --noinput")
        
        # Test finding a static file
        try:
            css_file = finders.find('css/style.css')
            if css_file:
                print(f"✅ Found static file: {css_file}")
            else:
                print("⚠️  CSS file not found - check static files")
        except Exception as e:
            print(f"⚠️  Static file finder error: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Static files check failed: {e}")
        return False

def test_media_files():
    """Test media files configuration"""
    print("\n🖼️  Testing Media Files Configuration...")
    
    try:
        from django.conf import settings
        
        print(f"MEDIA_URL: {settings.MEDIA_URL}")
        print(f"MEDIA_ROOT: {settings.MEDIA_ROOT}")
        
        # Check if media directory exists
        if hasattr(settings, 'MEDIA_ROOT') and settings.MEDIA_ROOT:
            if os.path.exists(str(settings.MEDIA_ROOT)):
                print("✅ MEDIA_ROOT directory exists")
            else:
                print("⚠️  MEDIA_ROOT directory doesn't exist")
                print("   Creating media directory...")
                os.makedirs(str(settings.MEDIA_ROOT), exist_ok=True)
        
        return True
        
    except Exception as e:
        print(f"❌ Media files check failed: {e}")
        return False

def main():
    """Main function to test URL routing"""
    print("🚀 Testing Django URL Routing and Configuration")
    print("=" * 50)
    
    # Setup Django
    try:
        setup_django()
        print("✅ Django setup successful")
    except Exception as e:
        print(f"❌ Django setup failed: {e}")
        return
    
    # Run tests
    tests = [
        ("URL Patterns", test_url_patterns),
        ("Static Files", test_static_files),
        ("Media Files", test_media_files),
    ]
    
    results = []
    for name, test_func in tests:
        print(f"\n--- {name} ---")
        try:
            result = test_func()
            results.append((name, result))
        except Exception as e:
            print(f"❌ {name} failed: {e}")
            results.append((name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 TEST RESULTS:")
    
    all_passed = True
    for name, result in results:
        status = "✅" if result else "❌"
        print(f"{status} {name}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 All tests passed! Your Django configuration looks good.")
        print("\n🔧 Next steps for cPanel deployment:")
        print("1. Upload the updated .htaccess file")
        print("2. Restart the Python application in cPanel")
        print("3. Run: python manage.py collectstatic --noinput")
        print("4. Test the URLs on your live site")
    else:
        print("\n⚠️  Some tests failed. Please fix the issues above.")

if __name__ == "__main__":
    main()
