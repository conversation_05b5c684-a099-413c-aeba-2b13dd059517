#!/usr/bin/env python3
"""
Complete Table Creation Script for PostgreSQL 10.x
This script creates ALL tables for the Django project with PostgreSQL 10.x compatible syntax
"""

import os
import sys
import django
from django.db import connection, transaction

def setup_django():
    """Setup Django environment"""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'sqlplayground.settings')
    django.setup()

def create_users_tables():
    """Create all users app tables"""
    print("🔧 Creating Users App Tables...")
    
    tables = [
        # Custom User table (extends AbstractUser)
        """
        CREATE TABLE IF NOT EXISTS users_user (
            id SERIAL PRIMARY KEY,
            password VARCHAR(128) NOT NULL,
            last_login TIMESTAMP WITH TIME ZONE,
            is_superuser BOOLEAN NOT NULL DEFAULT FALSE,
            username VARCHAR(150) NOT NULL UNIQUE,
            first_name VARCHAR(150) NOT NULL DEFAULT '',
            last_name VA<PERSON>HA<PERSON>(150) NOT NULL DEFAULT '',
            email VARCHAR(254) NOT NULL UNIQUE,
            is_staff BOOLEAN NOT NULL DEFAULT FALSE,
            is_active BOOLEAN NOT NULL DEFAULT TRUE,
            date_joined TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
            is_email_verified BOOLEAN NOT NULL DEFAULT FALSE,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
        );
        """,
        
        # User Profile table
        """
        CREATE TABLE IF NOT EXISTS users_userprofile (
            id SERIAL PRIMARY KEY,
            user_id INTEGER NOT NULL UNIQUE REFERENCES users_user(id) ON DELETE CASCADE,
            profile_picture VARCHAR(100),
            theme_preference VARCHAR(10) NOT NULL DEFAULT 'light',
            total_xp INTEGER NOT NULL DEFAULT 0 CHECK (total_xp >= 0),
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
        );
        """,
        
        # User Database table
        """
        CREATE TABLE IF NOT EXISTS users_userdatabase (
            id SERIAL PRIMARY KEY,
            user_id INTEGER NOT NULL UNIQUE REFERENCES users_user(id) ON DELETE CASCADE,
            database_path VARCHAR(255) NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
            last_accessed TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
        );
        """,
        
        # Email Verification Token table
        """
        CREATE TABLE IF NOT EXISTS users_emailverificationtoken (
            id SERIAL PRIMARY KEY,
            user_id INTEGER NOT NULL REFERENCES users_user(id) ON DELETE CASCADE,
            token UUID NOT NULL UNIQUE,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
            expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
            is_used BOOLEAN NOT NULL DEFAULT FALSE
        );
        """,
        
        # Password Reset Token table
        """
        CREATE TABLE IF NOT EXISTS users_passwordresettoken (
            id SERIAL PRIMARY KEY,
            user_id INTEGER NOT NULL REFERENCES users_user(id) ON DELETE CASCADE,
            token UUID NOT NULL UNIQUE,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
            expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
            is_used BOOLEAN NOT NULL DEFAULT FALSE
        );
        """
    ]
    
    try:
        with connection.cursor() as cursor:
            for i, table_sql in enumerate(tables, 1):
                print(f"   Creating users table {i}/{len(tables)}...")
                cursor.execute(table_sql)
        
        print("   ✅ Users tables created successfully")
        return True
        
    except Exception as e:
        print(f"   ❌ Error creating users tables: {e}")
        return False

def create_challenges_tables():
    """Create all challenges app tables"""
    print("🔧 Creating Challenges App Tables...")
    
    tables = [
        # Challenge table
        """
        CREATE TABLE IF NOT EXISTS challenges_challenge (
            id SERIAL PRIMARY KEY,
            title VARCHAR(255) NOT NULL,
            description TEXT NOT NULL,
            difficulty VARCHAR(10) NOT NULL DEFAULT 'easy',
            question TEXT NOT NULL,
            hint TEXT,
            schema_sql TEXT,
            run_dataset_sql TEXT,
            submit_dataset_sql TEXT,
            expected_query TEXT,
            expected_result JSONB DEFAULT '[]',
            sample_data VARCHAR(100),
            database_schema_type VARCHAR(20) NOT NULL DEFAULT 'employees',
            custom_initialization_sql TEXT,
            subscription_type VARCHAR(10) NOT NULL DEFAULT 'free',
            company VARCHAR(100),
            tags JSONB DEFAULT '[]',
            xp INTEGER NOT NULL DEFAULT 10 CHECK (xp >= 0),
            is_active BOOLEAN NOT NULL DEFAULT TRUE,
            "order" INTEGER NOT NULL DEFAULT 0 CHECK ("order" >= 0),
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
        );
        """,
        
        # Challenge Table (for multi-table challenges)
        """
        CREATE TABLE IF NOT EXISTS challenges_challengetable (
            id SERIAL PRIMARY KEY,
            challenge_id INTEGER NOT NULL REFERENCES challenges_challenge(id) ON DELETE CASCADE,
            table_name VARCHAR(100),
            schema_sql TEXT NOT NULL,
            run_dataset_sql TEXT NOT NULL,
            submit_dataset_sql TEXT NOT NULL,
            "order" INTEGER NOT NULL DEFAULT 0 CHECK ("order" >= 0),
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
            UNIQUE(challenge_id, table_name)
        );
        """,
        
        # User Challenge Progress
        """
        CREATE TABLE IF NOT EXISTS challenges_userchallengeProgress (
            id SERIAL PRIMARY KEY,
            user_id INTEGER NOT NULL REFERENCES users_user(id) ON DELETE CASCADE,
            challenge_id INTEGER NOT NULL REFERENCES challenges_challenge(id) ON DELETE CASCADE,
            is_completed BOOLEAN NOT NULL DEFAULT FALSE,
            completed_at TIMESTAMP WITH TIME ZONE,
            attempts INTEGER NOT NULL DEFAULT 0 CHECK (attempts >= 0),
            best_query TEXT,
            xp_earned INTEGER NOT NULL DEFAULT 0 CHECK (xp_earned >= 0),
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
            UNIQUE(user_id, challenge_id)
        );
        """,
        
        # XP Transaction
        """
        CREATE TABLE IF NOT EXISTS challenges_xptransaction (
            id SERIAL PRIMARY KEY,
            user_id INTEGER NOT NULL REFERENCES users_user(id) ON DELETE CASCADE,
            challenge_id INTEGER REFERENCES challenges_challenge(id) ON DELETE SET NULL,
            xp_amount INTEGER NOT NULL,
            transaction_type VARCHAR(20) NOT NULL DEFAULT 'challenge_completion',
            description TEXT,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
        );
        """,
        
        # Challenge Subscription Plan
        """
        CREATE TABLE IF NOT EXISTS challenges_challengesubscriptionplan (
            id SERIAL PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            duration_days INTEGER NOT NULL DEFAULT 30 CHECK (duration_days > 0),
            max_challenges INTEGER CHECK (max_challenges > 0),
            is_active BOOLEAN NOT NULL DEFAULT TRUE,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
        );
        """
    ]
    
    try:
        with connection.cursor() as cursor:
            for i, table_sql in enumerate(tables, 1):
                print(f"   Creating challenges table {i}/{len(tables)}...")
                cursor.execute(table_sql)
        
        print("   ✅ Challenges tables created successfully")
        return True
        
    except Exception as e:
        print(f"   ❌ Error creating challenges tables: {e}")
        return False

def create_courses_tables():
    """Create all courses app tables"""
    print("🔧 Creating Courses App Tables...")
    
    tables = [
        # Course table
        """
        CREATE TABLE IF NOT EXISTS courses_course (
            id SERIAL PRIMARY KEY,
            title VARCHAR(255) NOT NULL,
            slug VARCHAR(255) NOT NULL UNIQUE,
            description TEXT NOT NULL,
            short_description TEXT NOT NULL,
            difficulty VARCHAR(15) NOT NULL DEFAULT 'beginner',
            course_type VARCHAR(10) NOT NULL DEFAULT 'free',
            status VARCHAR(10) NOT NULL DEFAULT 'draft',
            price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            discount_price DECIMAL(10,2),
            thumbnail VARCHAR(100),
            preview_video VARCHAR(200),
            duration_hours INTEGER NOT NULL DEFAULT 0 CHECK (duration_hours >= 0),
            category VARCHAR(50),
            prerequisites TEXT,
            learning_outcomes JSONB DEFAULT '[]',
            is_featured BOOLEAN NOT NULL DEFAULT FALSE,
            "order" INTEGER NOT NULL DEFAULT 0 CHECK ("order" >= 0),
            published_at TIMESTAMP WITH TIME ZONE,
            instructor_id INTEGER NOT NULL REFERENCES users_user(id) ON DELETE CASCADE,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
        );
        """,
        
        # Course Module
        """
        CREATE TABLE IF NOT EXISTS courses_coursemodule (
            id SERIAL PRIMARY KEY,
            course_id INTEGER NOT NULL REFERENCES courses_course(id) ON DELETE CASCADE,
            title VARCHAR(255) NOT NULL,
            description TEXT,
            "order" INTEGER NOT NULL DEFAULT 0 CHECK ("order" >= 0),
            is_published BOOLEAN NOT NULL DEFAULT FALSE,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
        );
        """,
        
        # Subscription Plan
        """
        CREATE TABLE IF NOT EXISTS courses_subscriptionplan (
            id SERIAL PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            duration VARCHAR(20) NOT NULL DEFAULT 'monthly',
            is_active BOOLEAN NOT NULL DEFAULT TRUE,
            features JSONB DEFAULT '[]',
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
        );
        """
    ]
    
    try:
        with connection.cursor() as cursor:
            for i, table_sql in enumerate(tables, 1):
                print(f"   Creating courses table {i}/{len(tables)}...")
                cursor.execute(table_sql)
        
        print("   ✅ Courses tables created successfully")
        return True

    except Exception as e:
        print(f"   ❌ Error creating courses tables: {e}")
        return False

def create_tutorials_tables():
    """Create all tutorials app tables"""
    print("🔧 Creating Tutorials App Tables...")

    tables = [
        # Tutorial table
        """
        CREATE TABLE IF NOT EXISTS tutorials_tutorial (
            id SERIAL PRIMARY KEY,
            title VARCHAR(255) NOT NULL,
            slug VARCHAR(255) NOT NULL UNIQUE,
            description TEXT NOT NULL,
            content TEXT NOT NULL,
            difficulty VARCHAR(15) NOT NULL DEFAULT 'beginner',
            category VARCHAR(50),
            tags JSONB DEFAULT '[]',
            is_published BOOLEAN NOT NULL DEFAULT FALSE,
            "order" INTEGER NOT NULL DEFAULT 0 CHECK ("order" >= 0),
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
        );
        """,

        # Lesson table
        """
        CREATE TABLE IF NOT EXISTS tutorials_lesson (
            id SERIAL PRIMARY KEY,
            tutorial_id INTEGER REFERENCES tutorials_tutorial(id) ON DELETE CASCADE,
            title VARCHAR(255) NOT NULL,
            content TEXT NOT NULL,
            video_url VARCHAR(200),
            attachments JSONB DEFAULT '[]',
            "order" INTEGER NOT NULL DEFAULT 0 CHECK ("order" >= 0),
            is_published BOOLEAN NOT NULL DEFAULT FALSE,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
        );
        """
    ]

    try:
        with connection.cursor() as cursor:
            for i, table_sql in enumerate(tables, 1):
                print(f"   Creating tutorials table {i}/{len(tables)}...")
                cursor.execute(table_sql)

        print("   ✅ Tutorials tables created successfully")
        return True

    except Exception as e:
        print(f"   ❌ Error creating tutorials tables: {e}")
        return False

def create_core_tables():
    """Create all core app tables"""
    print("🔧 Creating Core App Tables...")

    tables = [
        # Site Settings table
        """
        CREATE TABLE IF NOT EXISTS core_sitesettings (
            id SERIAL PRIMARY KEY,
            site_name VARCHAR(100) NOT NULL DEFAULT 'KodeSQL',
            site_description TEXT,
            contact_email VARCHAR(254),
            maintenance_mode BOOLEAN NOT NULL DEFAULT FALSE,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
        );
        """
    ]

    try:
        with connection.cursor() as cursor:
            for i, table_sql in enumerate(tables, 1):
                print(f"   Creating core table {i}/{len(tables)}...")
                cursor.execute(table_sql)

        print("   ✅ Core tables created successfully")
        return True

    except Exception as e:
        print(f"   ❌ Error creating core tables: {e}")
        return False

def create_editor_tables():
    """Create all editor app tables"""
    print("🔧 Creating Editor App Tables...")

    tables = [
        # Query History table
        """
        CREATE TABLE IF NOT EXISTS editor_queryhistory (
            id SERIAL PRIMARY KEY,
            user_id INTEGER REFERENCES users_user(id) ON DELETE CASCADE,
            query_text TEXT NOT NULL,
            database_engine VARCHAR(20) NOT NULL DEFAULT 'postgresql',
            execution_time DECIMAL(10,6),
            result_count INTEGER,
            is_successful BOOLEAN NOT NULL DEFAULT TRUE,
            error_message TEXT,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
        );
        """
    ]

    try:
        with connection.cursor() as cursor:
            for i, table_sql in enumerate(tables, 1):
                print(f"   Creating editor table {i}/{len(tables)}...")
                cursor.execute(table_sql)

        print("   ✅ Editor tables created successfully")
        return True

    except Exception as e:
        print(f"   ❌ Error creating editor tables: {e}")
        return False

def create_indexes():
    """Create essential indexes for performance"""
    print("🔧 Creating Database Indexes...")

    indexes = [
        # Users indexes
        "CREATE INDEX IF NOT EXISTS users_user_email_idx ON users_user(email);",
        "CREATE INDEX IF NOT EXISTS users_user_username_idx ON users_user(username);",
        "CREATE INDEX IF NOT EXISTS users_userprofile_user_id_idx ON users_userprofile(user_id);",

        # Challenges indexes
        "CREATE INDEX IF NOT EXISTS challenges_challenge_difficulty_idx ON challenges_challenge(difficulty);",
        "CREATE INDEX IF NOT EXISTS challenges_challenge_subscription_type_idx ON challenges_challenge(subscription_type);",
        "CREATE INDEX IF NOT EXISTS challenges_challenge_is_active_idx ON challenges_challenge(is_active);",
        "CREATE INDEX IF NOT EXISTS challenges_userchallengeProgress_user_id_idx ON challenges_userchallengeProgress(user_id);",
        "CREATE INDEX IF NOT EXISTS challenges_userchallengeProgress_challenge_id_idx ON challenges_userchallengeProgress(challenge_id);",

        # Courses indexes
        "CREATE INDEX IF NOT EXISTS courses_course_slug_idx ON courses_course(slug);",
        "CREATE INDEX IF NOT EXISTS courses_course_status_idx ON courses_course(status);",
        "CREATE INDEX IF NOT EXISTS courses_course_course_type_idx ON courses_course(course_type);",

        # Editor indexes
        "CREATE INDEX IF NOT EXISTS editor_queryhistory_user_id_idx ON editor_queryhistory(user_id);",
        "CREATE INDEX IF NOT EXISTS editor_queryhistory_created_at_idx ON editor_queryhistory(created_at);",
    ]

    try:
        with connection.cursor() as cursor:
            for index_sql in indexes:
                cursor.execute(index_sql)

        print("   ✅ Database indexes created successfully")
        return True

    except Exception as e:
        print(f"   ❌ Error creating indexes: {e}")
        return False

def fake_all_migrations():
    """Fake all migrations to mark them as applied"""
    print("🔧 Faking All Migrations...")

    try:
        from django.core.management import execute_from_command_line

        all_apps = [
            'contenttypes',
            'auth',
            'sessions',
            'admin',
            'sites',
            'account',
            'socialaccount',
            'admin_interface',
            'users',
            'challenges',
            'courses',
            'tutorials',
            'core',
            'editor'
        ]

        for app in all_apps:
            try:
                print(f"   Faking migrations for {app}...")
                execute_from_command_line(['manage.py', 'migrate', app, '--fake'])
            except Exception as e:
                print(f"   ⚠️  {app} fake migration issue: {str(e)[:50]}...")

        print("   ✅ All migrations faked successfully")
        return True

    except Exception as e:
        print(f"   ❌ Error faking migrations: {e}")
        return False

def create_superuser():
    """Create a superuser for admin access"""
    print("🔧 Creating Superuser...")

    try:
        from django.contrib.auth import get_user_model
        User = get_user_model()

        # Check if superuser already exists
        if User.objects.filter(is_superuser=True).exists():
            print("   ✅ Superuser already exists")
            return True

        # Create superuser
        user = User.objects.create_superuser(
            username='admin',
            email='<EMAIL>',
            password='KodeSQL@1995'
        )

        print(f"   ✅ Superuser created: {user.username}")
        print("   💡 Login: admin / KodeSQL@1995")
        return True

    except Exception as e:
        print(f"   ❌ Error creating superuser: {e}")
        return False

def verify_all_tables():
    """Verify that all tables were created successfully"""
    print("🔍 Verifying All Tables...")

    required_tables = [
        'users_user',
        'users_userprofile',
        'users_userdatabase',
        'users_emailverificationtoken',
        'users_passwordresettoken',
        'challenges_challenge',
        'challenges_challengetable',
        'challenges_userchallengeProgress',
        'challenges_xptransaction',
        'courses_course',
        'courses_coursemodule',
        'tutorials_tutorial',
        'tutorials_lesson',
        'core_sitesettings',
        'editor_queryhistory'
    ]

    try:
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT table_name FROM information_schema.tables
                WHERE table_schema = 'public'
            """)
            existing_tables = [row[0] for row in cursor.fetchall()]

        missing_tables = []
        for table in required_tables:
            if table in existing_tables:
                print(f"   ✅ {table}")
            else:
                print(f"   ❌ {table} - MISSING")
                missing_tables.append(table)

        if missing_tables:
            print(f"   ⚠️  Missing tables: {len(missing_tables)}")
            return False
        else:
            print(f"   ✅ All {len(required_tables)} tables verified")
            return True

    except Exception as e:
        print(f"   ❌ Error verifying tables: {e}")
        return False

def main():
    """Main function to create all tables"""
    print("🚀 Complete Table Creation for PostgreSQL 10.x")
    print("="*70)

    # Setup Django
    try:
        setup_django()
        print("✅ Django setup successful")
    except Exception as e:
        print(f"❌ Django setup failed: {e}")
        return

    # Create all tables
    table_creation_steps = [
        ("Users Tables", create_users_tables),
        ("Challenges Tables", create_challenges_tables),
        ("Courses Tables", create_courses_tables),
        ("Tutorials Tables", create_tutorials_tables),
        ("Core Tables", create_core_tables),
        ("Editor Tables", create_editor_tables),
        ("Database Indexes", create_indexes),
        ("Fake Migrations", fake_all_migrations),
        ("Create Superuser", create_superuser),
        ("Verify All Tables", verify_all_tables)
    ]

    failed_steps = []

    for step_name, step_func in table_creation_steps:
        print(f"\n📋 {step_name}...")
        if not step_func():
            failed_steps.append(step_name)

    print("\n" + "="*70)
    print("📋 TABLE CREATION COMPLETE")
    print("="*70)

    if failed_steps:
        print(f"\n⚠️  Issues with: {len(failed_steps)} steps")
        for step in failed_steps:
            print(f"   - {step}")
    else:
        print("\n🎉 ALL TABLES CREATED SUCCESSFULLY!")

    print("\n🚀 Next steps:")
    print("1. Test your domain: https://kodesql.in")
    print("2. Access admin panel: https://kodesql.in/admin/")
    print("3. Login with: admin / KodeSQL@1995")
    print("4. Test challenge solve page")
    print("5. All database functionality should now work!")

if __name__ == "__main__":
    main()
