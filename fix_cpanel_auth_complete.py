#!/usr/bin/env python3
"""
Complete fix for cPanel authentication 500 errors
This script addresses all identified issues causing authentication failures
"""

import os
import sys
import django

def setup_django():
    """Setup Django environment"""
    sys.path.insert(0, os.getcwd())
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'sqlplayground.settings')
    django.setup()

def fix_django_sites():
    """Fix Django Sites framework configuration"""
    print("🔧 Fixing Django Sites Configuration...")
    
    try:
        from django.contrib.sites.models import Site
        from django.conf import settings
        
        site_id = getattr(settings, 'SITE_ID', 1)
        
        # Check if site exists
        try:
            site = Site.objects.get(id=site_id)
            if site.domain != 'kodesql.in':
                site.domain = 'kodesql.in'
                site.name = 'KodeSQL'
                site.save()
                print(f"✅ Updated site domain to: {site.domain}")
            else:
                print(f"✅ Site already configured: {site.domain}")
        except Site.DoesNotExist:
            Site.objects.create(id=site_id, domain='kodesql.in', name='KodeSQL')
            print("✅ Created new site: kodesql.in")
        
        return True
        
    except Exception as e:
        print(f"❌ Sites configuration failed: {e}")
        return False

def run_migrations():
    """Run Django migrations"""
    print("\n🔧 Running Django Migrations...")
    
    try:
        from django.core.management import execute_from_command_line
        
        # Run migrations
        execute_from_command_line(['manage.py', 'migrate', '--verbosity=1'])
        print("✅ Migrations completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        return False

def collect_static_files():
    """Collect static files for production"""
    print("\n🔧 Collecting Static Files...")
    
    try:
        from django.core.management import execute_from_command_line
        
        # Collect static files
        execute_from_command_line(['manage.py', 'collectstatic', '--noinput', '--verbosity=1'])
        print("✅ Static files collected successfully")
        return True
        
    except Exception as e:
        print(f"❌ Static files collection failed: {e}")
        return False

def test_authentication_views():
    """Test authentication views"""
    print("\n🔧 Testing Authentication Views...")
    
    try:
        from django.test import RequestFactory
        from django.contrib.messages.storage.fallback import FallbackStorage
        from users.views import login_view, register_view
        from users.forms import UserLoginForm, UserRegistrationForm
        
        factory = RequestFactory()
        
        # Test login view
        request = factory.get('/auth/login/')
        request.user = None
        request.session = {}
        setattr(request, '_messages', FallbackStorage(request))
        
        response = login_view(request)
        print(f"✅ Login view: {response.status_code}")
        
        # Test register view
        request = factory.get('/auth/register/')
        request.user = None
        request.session = {}
        setattr(request, '_messages', FallbackStorage(request))
        
        response = register_view(request)
        print(f"✅ Register view: {response.status_code}")
        
        # Test forms
        login_form = UserLoginForm()
        register_form = UserRegistrationForm()
        print("✅ Forms instantiation successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Authentication views test failed: {e}")
        return False

def create_superuser_if_needed():
    """Create superuser if none exists"""
    print("\n🔧 Checking Superuser...")
    
    try:
        from users.models import User
        
        if not User.objects.filter(is_superuser=True).exists():
            print("⚠️  No superuser found. You should create one after deployment:")
            print("   python manage.py createsuperuser")
        else:
            print("✅ Superuser exists")
        
        return True
        
    except Exception as e:
        print(f"❌ Superuser check failed: {e}")
        return False

def verify_database_connection():
    """Verify database connection"""
    print("\n🔧 Verifying Database Connection...")
    
    try:
        from django.db import connection
        
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
        
        print("✅ Database connection successful")
        return True
        
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        print("💡 Check your database credentials in .env file")
        return False

def main():
    """Main function to fix all authentication issues"""
    print("🚀 KodeSQL cPanel Authentication Fix")
    print("=" * 50)
    
    # Setup Django
    try:
        setup_django()
        print("✅ Django setup successful")
    except Exception as e:
        print(f"❌ Django setup failed: {e}")
        return
    
    # Run all fixes
    fixes = [
        ("Database Connection", verify_database_connection),
        ("Django Sites", fix_django_sites),
        ("Run Migrations", run_migrations),
        ("Collect Static Files", collect_static_files),
        ("Test Auth Views", test_authentication_views),
        ("Check Superuser", create_superuser_if_needed),
    ]
    
    results = []
    for name, fix_func in fixes:
        print(f"\n--- {name} ---")
        try:
            result = fix_func()
            results.append((name, result))
        except Exception as e:
            print(f"❌ {name} failed: {e}")
            results.append((name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 FIX RESULTS SUMMARY:")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for name, result in results:
        status = "✅" if result else "❌"
        print(f"{status} {name}")
    
    print(f"\nOverall: {passed}/{total} fixes successful")
    
    if passed >= total - 1:  # Allow one fix to fail
        print("\n🎉 Authentication should work now!")
        print("\n📋 Final Steps:")
        print("1. Upload all files to cPanel")
        print("2. Restart Python application")
        print("3. Test login/signup at https://kodesql.in/auth/login/")
        print("4. Create superuser if needed")
    else:
        print("\n❌ Multiple issues remain. Check the failed fixes above.")

if __name__ == "__main__":
    main()
