"""
Debug routing view to test if Django URL routing is working
Add this to your main urls.py to test routing
"""

from django.http import HttpResponse
from django.urls import path
import os

def debug_routing_view(request):
    """Debug view to test routing"""
    
    # Get environment info
    python_path = os.environ.get('PATH', 'Not set')
    django_settings = os.environ.get('DJANGO_SETTINGS_MODULE', 'Not set')
    
    html = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>🐛 Django Routing Debug</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 40px; }}
            .success {{ color: green; background: #f0fff0; padding: 10px; border-radius: 5px; }}
            .info {{ background: #f0f0f0; padding: 10px; margin: 10px 0; border-radius: 5px; }}
            .nav {{ background: #e0e0e0; padding: 15px; margin: 20px 0; border-radius: 5px; }}
            .nav a {{ margin-right: 15px; text-decoration: none; color: #0066cc; }}
            .nav a:hover {{ text-decoration: underline; }}
        </style>
    </head>
    <body>
        <div class="success">
            <h1>🎉 Django Routing is Working!</h1>
            <p>If you can see this page, Django URL routing is functioning correctly.</p>
        </div>
        
        <div class="info">
            <h2>Request Information</h2>
            <p><strong>Path:</strong> {request.path}</p>
            <p><strong>Method:</strong> {request.method}</p>
            <p><strong>User:</strong> {request.user}</p>
            <p><strong>Is Authenticated:</strong> {request.user.is_authenticated if hasattr(request.user, 'is_authenticated') else 'Unknown'}</p>
            <p><strong>GET Parameters:</strong> {dict(request.GET)}</p>
        </div>
        
        <div class="info">
            <h2>Environment Information</h2>
            <p><strong>Django Settings:</strong> {django_settings}</p>
            <p><strong>Server Name:</strong> {request.META.get('SERVER_NAME', 'Unknown')}</p>
            <p><strong>HTTP Host:</strong> {request.META.get('HTTP_HOST', 'Unknown')}</p>
            <p><strong>Request URI:</strong> {request.META.get('REQUEST_URI', 'Unknown')}</p>
        </div>
        
        <div class="nav">
            <h2>Test Navigation</h2>
            <p>Try these links to test if other URLs work:</p>
            <a href="/">🏠 Home</a>
            <a href="/dashboard/">📊 Dashboard</a>
            <a href="/auth/login/">🔐 Login</a>
            <a href="/auth/register/">📝 Register</a>
            <a href="/admin/">⚙️ Admin</a>
            <a href="/about/">ℹ️ About</a>
        </div>
        
        <div class="info">
            <h2>Troubleshooting</h2>
            <p>If other links above give 404 errors, the issue is with:</p>
            <ul>
                <li><strong>.htaccess configuration</strong> - Not routing requests through Django</li>
                <li><strong>Passenger configuration</strong> - Python app not properly set up</li>
                <li><strong>URL patterns</strong> - Django URLs not configured correctly</li>
            </ul>
        </div>
        
        <div class="nav">
            <h2>Quick Fixes</h2>
            <ol>
                <li>Check cPanel Python App settings</li>
                <li>Restart Python application</li>
                <li>Try alternative .htaccess configuration</li>
                <li>Check error logs in cPanel</li>
            </ol>
        </div>
    </body>
    </html>
    """
    
    return HttpResponse(html)

# URL pattern to add to your main urls.py
debug_urlpatterns = [
    path('debug-routing/', debug_routing_view, name='debug_routing'),
]
