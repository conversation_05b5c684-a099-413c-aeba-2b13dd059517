#!/usr/bin/env python3
"""
Create Missing Course-Related Tables
This script creates all the missing course tables that are causing the 500 error
"""

import os
import sys
import django
from django.db import connection, transaction

def setup_django():
    """Setup Django environment"""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'sqlplayground.settings')
    django.setup()

def create_missing_course_tables():
    """Create all missing course-related tables"""
    print("🔧 Creating Missing Course Tables...")
    
    tables = [
        # User Course Enrollment table
        """
        CREATE TABLE IF NOT EXISTS courses_usercourseenrollment (
            id SERIAL PRIMARY KEY,
            user_id INTEGER NOT NULL REFERENCES users_user(id) ON DELETE CASCADE,
            course_id INTEGER NOT NULL REFERENCES courses_course(id) ON DELETE CASCADE,
            status VARCHAR(20) NOT NULL DEFAULT 'pending',
            enrolled_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
            completed_at TIMESTAMP WITH TIME ZONE,
            last_accessed TIMESTAMP WITH TIME ZONE,
            progress_percentage DECIMAL(5,2) NOT NULL DEFAULT 0.00,
            amount_paid DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            payment_method VARCHAR(50),
            current_module_id INTEGER,
            current_lesson_id INTEGER,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
            UNIQUE(user_id, course_id)
        );
        """,
        
        # Course Lesson table
        """
        CREATE TABLE IF NOT EXISTS courses_courselesson (
            id SERIAL PRIMARY KEY,
            module_id INTEGER NOT NULL REFERENCES courses_coursemodule(id) ON DELETE CASCADE,
            title VARCHAR(255) NOT NULL,
            slug VARCHAR(255) NOT NULL,
            content TEXT NOT NULL,
            video_url VARCHAR(500),
            video_duration INTEGER DEFAULT 0,
            "order" INTEGER NOT NULL DEFAULT 0 CHECK ("order" >= 0),
            is_preview BOOLEAN NOT NULL DEFAULT FALSE,
            is_active BOOLEAN NOT NULL DEFAULT TRUE,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
        );
        """,
        
        # Course Review table
        """
        CREATE TABLE IF NOT EXISTS courses_coursereview (
            id SERIAL PRIMARY KEY,
            user_id INTEGER NOT NULL REFERENCES users_user(id) ON DELETE CASCADE,
            course_id INTEGER NOT NULL REFERENCES courses_course(id) ON DELETE CASCADE,
            rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
            review_text TEXT,
            is_approved BOOLEAN NOT NULL DEFAULT TRUE,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
            UNIQUE(user_id, course_id)
        );
        """,
        
        # Lesson Resource table
        """
        CREATE TABLE IF NOT EXISTS courses_lessonresource (
            id SERIAL PRIMARY KEY,
            lesson_id INTEGER NOT NULL REFERENCES courses_courselesson(id) ON DELETE CASCADE,
            title VARCHAR(255) NOT NULL,
            resource_type VARCHAR(20) NOT NULL DEFAULT 'file',
            file_path VARCHAR(500),
            external_url VARCHAR(500),
            description TEXT,
            is_downloadable BOOLEAN NOT NULL DEFAULT TRUE,
            "order" INTEGER NOT NULL DEFAULT 0 CHECK ("order" >= 0),
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
        );
        """,
        
        # Course Certificate table
        """
        CREATE TABLE IF NOT EXISTS courses_coursecertificate (
            id SERIAL PRIMARY KEY,
            enrollment_id INTEGER NOT NULL UNIQUE REFERENCES courses_usercourseenrollment(id) ON DELETE CASCADE,
            certificate_id VARCHAR(100) NOT NULL UNIQUE,
            student_name VARCHAR(255) NOT NULL,
            course_title VARCHAR(255) NOT NULL,
            completion_date DATE NOT NULL,
            instructor_name VARCHAR(255) NOT NULL,
            is_valid BOOLEAN NOT NULL DEFAULT TRUE,
            issued_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
        );
        """,
        
        # Course Payment table
        """
        CREATE TABLE IF NOT EXISTS courses_coursepayment (
            id SERIAL PRIMARY KEY,
            enrollment_id INTEGER NOT NULL REFERENCES courses_usercourseenrollment(id) ON DELETE CASCADE,
            payment_id VARCHAR(100) NOT NULL UNIQUE,
            amount DECIMAL(10,2) NOT NULL,
            currency VARCHAR(3) NOT NULL DEFAULT 'USD',
            payment_method VARCHAR(50) NOT NULL,
            payment_status VARCHAR(20) NOT NULL DEFAULT 'pending',
            transaction_id VARCHAR(255),
            payment_gateway VARCHAR(50),
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
        );
        """,
        
        # User Subscription table
        """
        CREATE TABLE IF NOT EXISTS courses_usersubscription (
            id SERIAL PRIMARY KEY,
            user_id INTEGER NOT NULL REFERENCES users_user(id) ON DELETE CASCADE,
            course_id INTEGER NOT NULL REFERENCES courses_course(id) ON DELETE CASCADE,
            plan_id INTEGER NOT NULL REFERENCES courses_subscriptionplan(id) ON DELETE CASCADE,
            enrollment_id INTEGER REFERENCES courses_usercourseenrollment(id) ON DELETE CASCADE,
            status VARCHAR(20) NOT NULL DEFAULT 'pending',
            start_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
            end_date TIMESTAMP WITH TIME ZONE,
            auto_renew BOOLEAN NOT NULL DEFAULT FALSE,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
        );
        """
    ]
    
    try:
        with connection.cursor() as cursor:
            for i, table_sql in enumerate(tables, 1):
                print(f"   Creating course table {i}/{len(tables)}...")
                cursor.execute(table_sql)
        
        print("   ✅ Missing course tables created successfully")
        return True
        
    except Exception as e:
        print(f"   ❌ Error creating course tables: {e}")
        return False

def create_course_indexes():
    """Create indexes for the new course tables"""
    print("🔧 Creating Course Table Indexes...")
    
    indexes = [
        # Enrollment indexes
        "CREATE INDEX IF NOT EXISTS courses_usercourseenrollment_user_id_idx ON courses_usercourseenrollment(user_id);",
        "CREATE INDEX IF NOT EXISTS courses_usercourseenrollment_course_id_idx ON courses_usercourseenrollment(course_id);",
        "CREATE INDEX IF NOT EXISTS courses_usercourseenrollment_status_idx ON courses_usercourseenrollment(status);",
        
        # Review indexes
        "CREATE INDEX IF NOT EXISTS courses_coursereview_course_id_idx ON courses_coursereview(course_id);",
        "CREATE INDEX IF NOT EXISTS courses_coursereview_user_id_idx ON courses_coursereview(user_id);",
        "CREATE INDEX IF NOT EXISTS courses_coursereview_rating_idx ON courses_coursereview(rating);",
        "CREATE INDEX IF NOT EXISTS courses_coursereview_is_approved_idx ON courses_coursereview(is_approved);",
        
        # Lesson indexes
        "CREATE INDEX IF NOT EXISTS courses_courselesson_module_id_idx ON courses_courselesson(module_id);",
        "CREATE INDEX IF NOT EXISTS courses_courselesson_order_idx ON courses_courselesson(\"order\");",
        
        # Resource indexes
        "CREATE INDEX IF NOT EXISTS courses_lessonresource_lesson_id_idx ON courses_lessonresource(lesson_id);",
    ]
    
    try:
        with connection.cursor() as cursor:
            for index_sql in indexes:
                cursor.execute(index_sql)
        
        print("   ✅ Course table indexes created successfully")
        return True
        
    except Exception as e:
        print(f"   ❌ Error creating course indexes: {e}")
        return False

def verify_course_tables():
    """Verify all course tables exist"""
    print("🔍 Verifying Course Tables...")
    
    required_tables = [
        'courses_course',
        'courses_coursemodule',
        'courses_usercourseenrollment',
        'courses_courselesson',
        'courses_coursereview',
        'courses_lessonresource',
        'courses_coursecertificate',
        'courses_coursepayment',
        'courses_subscriptionplan',
        'courses_usersubscription'
    ]
    
    try:
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT table_name FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name LIKE 'courses_%'
            """)
            existing_tables = [row[0] for row in cursor.fetchall()]
        
        missing_tables = []
        for table in required_tables:
            if table in existing_tables:
                print(f"   ✅ {table}")
            else:
                print(f"   ❌ {table} - MISSING")
                missing_tables.append(table)
        
        if missing_tables:
            print(f"   ⚠️  Missing tables: {len(missing_tables)}")
            return False
        else:
            print(f"   ✅ All {len(required_tables)} course tables verified")
            return True
            
    except Exception as e:
        print(f"   ❌ Error verifying course tables: {e}")
        return False

def test_landing_page_query():
    """Test the specific query that was failing in the landing page"""
    print("🔍 Testing Landing Page Query...")
    
    try:
        from courses.models import Course
        from django.db.models import Count, Avg, Q
        
        # This is the exact query from the landing page that was failing
        featured_courses = Course.objects.filter(
            status='published',
            is_featured=True
        ).select_related('instructor').annotate(
            enrollment_count=Count('enrollments', filter=Q(enrollments__status__in=['active', 'completed'])),
            avg_rating=Avg('reviews__rating', filter=Q(reviews__is_approved=True))
        ).order_by('order')[:3]
        
        # Try to evaluate the queryset
        course_list = list(featured_courses)
        
        print(f"   ✅ Landing page query successful: {len(course_list)} featured courses")
        return True
        
    except Exception as e:
        print(f"   ❌ Landing page query failed: {e}")
        return False

def main():
    """Main function"""
    print("🚀 Create Missing Course Tables")
    print("="*50)
    
    # Setup Django
    try:
        setup_django()
        print("✅ Django setup successful")
    except Exception as e:
        print(f"❌ Django setup failed: {e}")
        return
    
    # Run all steps
    steps = [
        ("Create Missing Course Tables", create_missing_course_tables),
        ("Create Course Indexes", create_course_indexes),
        ("Verify Course Tables", verify_course_tables),
        ("Test Landing Page Query", test_landing_page_query)
    ]
    
    failed_steps = []
    
    for step_name, step_func in steps:
        print(f"\n📋 {step_name}...")
        if not step_func():
            failed_steps.append(step_name)
    
    print("\n" + "="*50)
    print("📋 COURSE TABLES CREATION COMPLETE")
    print("="*50)
    
    if failed_steps:
        print(f"\n⚠️  Issues with: {len(failed_steps)} steps")
        for step in failed_steps:
            print(f"   - {step}")
    else:
        print("\n🎉 ALL COURSE TABLES CREATED SUCCESSFULLY!")
    
    print("\n🚀 Next steps:")
    print("1. Test your domain: https://kodesql.in")
    print("2. The 500 error should now be completely resolved")
    print("3. Landing page should load properly")
    print("4. All course functionality should work")

if __name__ == "__main__":
    main()
