#!/usr/bin/env python3
"""
EMERGENCY FIX: Create missing users_passwordresettoken table
This specifically fixes the admin panel error for deleting users
"""

import os
import sys

def setup_django():
    """Setup Django environment"""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'sqlplayground.settings')
    os.environ.setdefault('DEBUG', 'False')
    
    try:
        import django
        django.setup()
        print("SUCCESS: Django setup successful")
        return True
    except Exception as e:
        print(f"ERROR: Django setup failed: {e}")
        return False

def check_database_connection():
    """Check database connection"""
    print("Checking database connection...")
    
    try:
        from django.db import connection
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            if result:
                print("SUCCESS: Database connection successful")
                return True
    except Exception as e:
        print(f"ERROR: Database connection failed: {e}")
        return False

def check_missing_token_tables():
    """Check specifically for missing token tables"""
    print("\nChecking token tables...")
    
    try:
        from django.db import connection
        
        token_tables = [
            'users_passwordresettoken',
            'users_emailverificationtoken',
        ]
        
        missing = []
        
        with connection.cursor() as cursor:
            for table in token_tables:
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_name = %s
                    )
                """, [table])
                
                exists = cursor.fetchone()[0]
                if exists:
                    print(f"EXISTS: {table}")
                else:
                    print(f"MISSING: {table}")
                    missing.append(table)
        
        return missing
        
    except Exception as e:
        print(f"ERROR: Token table check failed: {e}")
        return []

def create_password_reset_token_table():
    """Create the missing users_passwordresettoken table"""
    print("\nCreating users_passwordresettoken table...")
    
    try:
        from django.db import connection
        
        # SQL to create the PasswordResetToken table
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS users_passwordresettoken (
            id BIGSERIAL PRIMARY KEY,
            token UUID NOT NULL UNIQUE DEFAULT gen_random_uuid(),
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
            is_used BOOLEAN NOT NULL DEFAULT FALSE,
            user_id BIGINT NOT NULL REFERENCES users_user(id) ON DELETE CASCADE
        );
        
        CREATE INDEX IF NOT EXISTS users_passwordresettoken_user_id_idx 
        ON users_passwordresettoken(user_id);
        
        CREATE INDEX IF NOT EXISTS users_passwordresettoken_token_idx 
        ON users_passwordresettoken(token);
        
        CREATE INDEX IF NOT EXISTS users_passwordresettoken_created_at_idx 
        ON users_passwordresettoken(created_at);
        """
        
        with connection.cursor() as cursor:
            cursor.execute(create_table_sql)
            print("SUCCESS: users_passwordresettoken table created")
            
            return True
            
    except Exception as e:
        print(f"ERROR: Password reset token table creation failed: {e}")
        return False

def create_email_verification_token_table():
    """Create the missing users_emailverificationtoken table if needed"""
    print("\nCreating users_emailverificationtoken table...")
    
    try:
        from django.db import connection
        
        # SQL to create the EmailVerificationToken table
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS users_emailverificationtoken (
            id BIGSERIAL PRIMARY KEY,
            token UUID NOT NULL UNIQUE DEFAULT gen_random_uuid(),
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
            is_used BOOLEAN NOT NULL DEFAULT FALSE,
            user_id BIGINT NOT NULL REFERENCES users_user(id) ON DELETE CASCADE
        );
        
        CREATE INDEX IF NOT EXISTS users_emailverificationtoken_user_id_idx 
        ON users_emailverificationtoken(user_id);
        
        CREATE INDEX IF NOT EXISTS users_emailverificationtoken_token_idx 
        ON users_emailverificationtoken(token);
        
        CREATE INDEX IF NOT EXISTS users_emailverificationtoken_created_at_idx 
        ON users_emailverificationtoken(created_at);
        """
        
        with connection.cursor() as cursor:
            cursor.execute(create_table_sql)
            print("SUCCESS: users_emailverificationtoken table created")
            
            return True
            
    except Exception as e:
        print(f"ERROR: Email verification token table creation failed: {e}")
        return False

def test_token_models():
    """Test if token models work"""
    print("\nTesting token models...")
    
    try:
        from users.models import PasswordResetToken, EmailVerificationToken, User
        
        # Test PasswordResetToken model
        token_count = PasswordResetToken.objects.count()
        print(f"SUCCESS: PasswordResetToken model works - {token_count} tokens")
        
        # Test EmailVerificationToken model
        email_token_count = EmailVerificationToken.objects.count()
        print(f"SUCCESS: EmailVerificationToken model works - {email_token_count} tokens")
        
        # Test if we can create a token for an existing user
        user = User.objects.first()
        if user:
            # Test creating password reset token
            reset_token, created = PasswordResetToken.objects.get_or_create(
                user=user,
                is_used=False,
                defaults={'token': 'test-token-123'}
            )
            if created:
                print(f"SUCCESS: Created test PasswordResetToken for user: {user.email}")
                # Clean up test token
                reset_token.delete()
            else:
                print(f"SUCCESS: PasswordResetToken already exists for user: {user.email}")
        else:
            print("WARNING: No users found to test with")
        
        return True
        
    except Exception as e:
        print(f"ERROR: Token model test failed: {e}")
        return False

def test_admin_user_deletion():
    """Test if admin can now handle user operations"""
    print("\nTesting admin user operations...")
    
    try:
        from django.contrib.auth import get_user_model
        from users.models import PasswordResetToken, EmailVerificationToken
        
        User = get_user_model()
        
        # Test basic user queries
        user_count = User.objects.count()
        print(f"SUCCESS: Can query users: {user_count} users")
        
        # Test related token queries
        reset_tokens = PasswordResetToken.objects.count()
        email_tokens = EmailVerificationToken.objects.count()
        print(f"SUCCESS: Can query reset tokens: {reset_tokens}")
        print(f"SUCCESS: Can query email tokens: {email_tokens}")
        
        # Test if we can access user relationships (what admin panel does)
        if user_count > 0:
            first_user = User.objects.first()
            user_reset_tokens = PasswordResetToken.objects.filter(user=first_user).count()
            user_email_tokens = EmailVerificationToken.objects.filter(user=first_user).count()
            print(f"SUCCESS: User has {user_reset_tokens} reset tokens and {user_email_tokens} email tokens")
        
        return True
        
    except Exception as e:
        print(f"ERROR: Admin user operation test failed: {e}")
        return False

def main():
    """Main emergency fix function"""
    print("EMERGENCY FIX: users_passwordresettoken Table")
    print("=" * 50)
    print("Fixing admin panel error: relation 'users_passwordresettoken' does not exist")
    
    # Setup Django
    if not setup_django():
        return
    
    # Check database connection
    if not check_database_connection():
        return
    
    # Check missing token tables
    missing_tables = check_missing_token_tables()
    
    if missing_tables:
        print(f"\nFound {len(missing_tables)} missing token tables")
        
        # Create missing tables
        success = True
        
        if 'users_passwordresettoken' in missing_tables:
            if not create_password_reset_token_table():
                success = False
        
        if 'users_emailverificationtoken' in missing_tables:
            if not create_email_verification_token_table():
                success = False
        
        if success:
            # Test token models
            if test_token_models():
                print("SUCCESS: Token models working")
                
                # Test admin functionality
                if test_admin_user_deletion():
                    print("\nSUCCESS: EMERGENCY FIX COMPLETE!")
                    print("\nNext steps:")
                    print("1. Restart your Python app in cPanel")
                    print("2. Test admin panel: https://kodesql.in/admin/")
                    print("3. Try accessing Users section in admin")
                    print("4. Try deleting a user - should work now")
                    print("5. Password reset functionality should work")
                else:
                    print("\nWARNING: Tables created but admin still has issues")
            else:
                print("\nWARNING: Tables created but models have issues")
        else:
            print("\nERROR: Failed to create some tables")
    else:
        print("\nAll token tables exist")
        
        # Still test functionality
        if test_token_models() and test_admin_user_deletion():
            print("SUCCESS: All token functionality working")
        else:
            print("WARNING: Token functionality has issues")

if __name__ == "__main__":
    main()
