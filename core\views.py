from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse, HttpResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.contrib import messages
from django.middleware.csrf import get_token
from django.contrib.auth import get_user_model
from editor.models import QueryHistory, SavedQuery
from challenges.models import UserChallengeProgress
from tutorials.models import UserTutorialProgress

User = get_user_model()


def landing_page(request):
    """
    Landing page for new visitors.
    """
    from courses.models import Course
    from django.db.models import Count, Avg, Q

    # Get featured courses (limit to 3 for landing page)
    featured_courses = Course.objects.filter(
        status='published',
        is_featured=True
    ).select_related('instructor').annotate(
        enrollment_count=Count('enrollments', filter=Q(enrollments__status__in=['active', 'completed'])),
        avg_rating=Avg('reviews__rating', filter=Q(reviews__is_approved=True))
    ).order_by('order')[:3]

    # If no featured courses, get the latest published courses
    if not featured_courses:
        featured_courses = Course.objects.filter(
            status='published'
        ).select_related('instructor').annotate(
            enrollment_count=Count('enrollments', filter=Q(enrollments__status__in=['active', 'completed'])),
            avg_rating=Avg('reviews__rating', filter=Q(reviews__is_approved=True))
        ).order_by('-created_at')[:3]

    context = {
        'page_title': 'SQL Master - Learn SQL the Right Way',
        'featured_courses': featured_courses,
    }
    return render(request, 'core/landing_page.html', context)


def home(request):
    """
    Home page with SQL editor.
    """
    context = {
        'page_title': 'SQL Editor',
        'csrf_token': get_token(request),
    }
    return render(request, 'core/home.html', context)


@login_required
def dashboard(request):
    """
    User dashboard with gamification and challenge-focused statistics.
    """
    from challenges.models import Challenge, UserChallengeProgress, UserChallengeSubscription
    from django.db.models import Sum, Count, Q, Avg
    from django.utils import timezone
    from datetime import timedelta

    user = request.user

    # Ensure user has a profile and XP is up-to-date
    from users.models import UserProfile
    try:
        profile = user.profile
    except UserProfile.DoesNotExist:
        profile = UserProfile.objects.create(user=user)

    # Update total XP to ensure consistency
    profile.update_total_xp()

    # Challenge Statistics
    total_challenges = Challenge.objects.filter(is_active=True).count()
    user_progress = UserChallengeProgress.objects.filter(user=user)
    completed_challenges = user_progress.filter(is_completed=True).count()
    attempted_challenges = user_progress.count()

    # XP Statistics - use updated profile XP for consistency
    total_xp = profile.total_xp

    # Calculate current streak using the proven algorithm
    from challenges.views import calculate_user_streak
    current_streak = calculate_user_streak(user)

    # Difficulty breakdown
    difficulty_stats = {}
    for difficulty in ['easy', 'medium', 'hard', 'extreme']:
        total_diff = Challenge.objects.filter(is_active=True, difficulty=difficulty).count()
        completed_diff = user_progress.filter(
            is_completed=True,
            challenge__difficulty=difficulty
        ).count()
        difficulty_stats[difficulty] = {
            'total': total_diff,
            'completed': completed_diff,
            'percentage': (completed_diff / total_diff * 100) if total_diff > 0 else 0
        }

    # Leaderboard (top 10 users by XP) with profile pictures
    # Get user IDs first, then fetch full user objects for profile pictures
    # Exclude superusers from leaderboard
    top_user_ids = UserChallengeProgress.objects.filter(
        is_completed=True,
        user__is_superuser=False  # Exclude superusers
    ).values('user_id').annotate(
        total_xp=Sum('xp_earned'),
        challenges_completed=Count('id')
    ).order_by('-total_xp')[:10]

    # Create leaderboard with full user objects
    leaderboard = []
    for entry in top_user_ids:
        try:
            user = User.objects.select_related('profile').get(id=entry['user_id'])
            leaderboard.append({
                'user': user,
                'total_xp': entry['total_xp'],
                'challenges_completed': entry['challenges_completed']
            })
        except User.DoesNotExist:
            continue

    # Add current user rank (excluding superusers)
    user_rank = None
    if total_xp > 0:
        higher_xp_users = UserChallengeProgress.objects.filter(
            is_completed=True,
            user__is_superuser=False  # Exclude superusers from ranking
        ).values('user').annotate(
            total_xp=Sum('xp_earned')
        ).filter(total_xp__gt=total_xp).count()
        user_rank = higher_xp_users + 1

    # Recent achievements (last 5 completed challenges)
    recent_achievements = user_progress.filter(
        is_completed=True
    ).select_related('challenge').order_by('-completed_at')[:5]

    # Subscription status
    user_subscription = UserChallengeSubscription.objects.filter(
        user=user, status='active'
    ).first()

    context = {
        'page_title': 'Dashboard',
        'total_challenges': total_challenges,
        'completed_challenges': completed_challenges,
        'attempted_challenges': attempted_challenges,
        'completion_percentage': (completed_challenges / total_challenges * 100) if total_challenges > 0 else 0,
        'total_xp': total_xp,
        'current_streak': current_streak,
        'user_rank': user_rank,
        'difficulty_stats': difficulty_stats,
        'leaderboard': leaderboard,
        'recent_achievements': recent_achievements,
        'user_subscription': user_subscription,
    }
    return render(request, 'core/dashboard.html', context)


def about(request):
    """
    About page.
    """
    context = {
        'page_title': 'About SQL Playground',
    }
    return render(request, 'core/about.html', context)


def contribute(request):
    """
    Contribute page.
    """
    context = {
        'page_title': 'Contribute',
    }
    return render(request, 'core/contribute.html', context)


def csrf_debug(request):
    """Debug view to check CSRF token generation and validation"""
    csrf_token = get_token(request)

    context = {
        'csrf_token': csrf_token,
        'csrf_token_length': len(csrf_token),
        'method': request.method,
        'has_session': hasattr(request, 'session'),
        'session_key': getattr(request.session, 'session_key', None) if hasattr(request, 'session') else None,
        'cookies': dict(request.COOKIES),
        'meta_csrf': request.META.get('CSRF_COOKIE', 'Not found'),
    }

    if request.method == 'POST':
        context.update({
            'post_data': dict(request.POST),
            'csrf_token_from_post': request.POST.get('csrfmiddlewaretoken', 'Not found'),
            'csrf_token_matches': request.POST.get('csrfmiddlewaretoken') == csrf_token,
        })

    return JsonResponse(context, json_dumps_params={'indent': 2})


@csrf_exempt
def csrf_test_form(request):
    """Test form for CSRF debugging"""
    if request.method == 'POST':
        return JsonResponse({
            'status': 'success',
            'message': 'Form submitted successfully without CSRF protection',
            'csrf_token_received': request.POST.get('csrfmiddlewaretoken', 'Not found')
        })

    return render(request, 'core/csrf_test.html')


def terms_of_service(request):
    """
    Terms of Service page.
    """
    context = {
        'page_title': 'Terms of Service - SQLMaster',
    }
    return render(request, 'core/terms_of_service.html', context)


def privacy_policy(request):
    """
    Privacy Policy page.
    """
    context = {
        'page_title': 'Privacy Policy - SQLMaster',
    }
    return render(request, 'core/privacy_policy.html', context)



def debug_info_view(request):
    """Debug information view for troubleshooting"""
    import sys
    import django
    from django.conf import settings
    
    debug_info = {
        'python_version': sys.version,
        'django_version': django.get_version(),
        'debug_mode': settings.DEBUG,
        'allowed_hosts': settings.ALLOWED_HOSTS,
        'static_url': settings.STATIC_URL,
        'static_root': getattr(settings, 'STATIC_ROOT', 'Not set'),
        'media_url': settings.MEDIA_URL,
        'media_root': getattr(settings, 'MEDIA_ROOT', 'Not set'),
        'database_engine': settings.DATABASES['default']['ENGINE'],
        'request_path': request.path,
        'request_method': request.method,
        'user_authenticated': request.user.is_authenticated,
    }
    
    return JsonResponse(debug_info, json_dumps_params={'indent': 2})


def url_debug_view(request):
    """Debug view to test URL routing and show what's working"""
    from django.urls import reverse, resolve
    from django.conf import settings

    # Test URL resolution
    url_tests = []
    test_paths = ['/', '/dashboard/', '/challenges/', '/auth/login/', '/auth/register/', '/admin/']

    for path in test_paths:
        try:
            match = resolve(path)
            url_tests.append({
                'path': path,
                'status': 'OK',
                'view_name': match.view_name,
                'view_func': f"{match.func.__module__}.{match.func.__name__}"
            })
        except Exception as e:
            url_tests.append({
                'path': path,
                'status': 'ERROR',
                'error': str(e)
            })

    # Test reverse URLs
    reverse_tests = []
    test_names = [
        'core:landing_page',
        'core:dashboard',
        'challenges:challenges_list',
        'users:login',
        'users:register'
    ]

    for name in test_names:
        try:
            url = reverse(name)
            reverse_tests.append({
                'name': name,
                'status': 'OK',
                'url': url
            })
        except Exception as e:
            reverse_tests.append({
                'name': name,
                'status': 'ERROR',
                'error': str(e)
            })

    html = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>URL Debug - KodeSQL</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 20px; }}
            .success {{ color: green; }}
            .error {{ color: red; }}
            .info {{ background: #f0f0f0; padding: 15px; margin: 15px 0; border-radius: 5px; }}
            table {{ border-collapse: collapse; width: 100%; margin: 15px 0; }}
            th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
            th {{ background-color: #f2f2f2; }}
            .nav {{ background: #e0e0e0; padding: 15px; margin: 20px 0; border-radius: 5px; }}
            .nav a {{ margin-right: 15px; text-decoration: none; color: #0066cc; }}
        </style>
    </head>
    <body>
        <h1>🐛 URL Debug - KodeSQL</h1>

        <div class="info">
            <h2>Current Request Info</h2>
            <p><strong>Path:</strong> {request.path}</p>
            <p><strong>Method:</strong> {request.method}</p>
            <p><strong>DEBUG:</strong> {settings.DEBUG}</p>
            <p><strong>ROOT_URLCONF:</strong> {settings.ROOT_URLCONF}</p>
            <p><strong>User:</strong> {request.user}</p>
        </div>

        <h2>URL Resolution Tests</h2>
        <table>
            <tr><th>Path</th><th>Status</th><th>View Name</th><th>Details</th></tr>
            {"".join([
                f"<tr><td>{test['path']}</td><td class='{'success' if test['status'] == 'OK' else 'error'}'>{test['status']}</td><td>{test.get('view_name', '')}</td><td>{test.get('view_func', test.get('error', ''))}</td></tr>"
                for test in url_tests
            ])}
        </table>

        <h2>Reverse URL Tests</h2>
        <table>
            <tr><th>URL Name</th><th>Status</th><th>Generated URL</th></tr>
            {"".join([
                f"<tr><td>{test['name']}</td><td class='{'success' if test['status'] == 'OK' else 'error'}'>{test['status']}</td><td>{test.get('url', test.get('error', ''))}</td></tr>"
                for test in reverse_tests
            ])}
        </table>

        <div class="nav">
            <h2>Test Navigation Links</h2>
            <p>Click these links to test if they work:</p>
            <a href="/">🏠 Home</a>
            <a href="/dashboard/">📊 Dashboard</a>
            <a href="/challenges/">🎯 Challenges</a>
            <a href="/auth/login/">🔐 Login</a>
            <a href="/auth/register/">📝 Register</a>
            <a href="/admin/">⚙️ Admin</a>
        </div>

        <div class="info">
            <h2>Diagnosis</h2>
            <p>If URLs show "OK" above but still give 404 when clicked:</p>
            <ul>
                <li>Check your template links (href attributes)</li>
                <li>Check form action attributes</li>
                <li>Check JavaScript AJAX requests</li>
                <li>Verify static files are loading</li>
            </ul>
        </div>
    </body>
    </html>
    """

    return HttpResponse(html)


def csrf_failure(request, reason=""):
    """
    Custom CSRF failure view with helpful debugging information.
    """
    csrf_token = get_token(request)

    html = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>CSRF Verification Failed</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }}
            .container {{ max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; }}
            .error {{ color: #d32f2f; background: #ffebee; padding: 15px; border-radius: 4px; margin: 20px 0; }}
            .info {{ background: #e3f2fd; padding: 15px; border-radius: 4px; margin: 20px 0; }}
            .solution {{ background: #e8f5e8; padding: 15px; border-radius: 4px; margin: 20px 0; }}
            .code {{ background: #f5f5f5; padding: 10px; border-radius: 4px; font-family: monospace; }}
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🚫 CSRF Verification Failed</h1>

            <div class="error">
                <h3>Error Details:</h3>
                <p><strong>Reason:</strong> {reason}</p>
                <p>The CSRF token was missing or incorrect.</p>
            </div>

            <div class="info">
                <h3>Debug Information:</h3>
                <p><strong>Current CSRF Token:</strong> {csrf_token}</p>
                <p><strong>Session Key:</strong> {request.session.session_key}</p>
                <p><strong>User Agent:</strong> {request.META.get('HTTP_USER_AGENT', 'Unknown')[:100]}...</p>
            </div>

            <div class="solution">
                <h3>💡 How to Fix:</h3>
                <ol>
                    <li><strong>Clear browser cookies and cache</strong></li>
                    <li><strong>Refresh the page</strong> and try again</li>
                    <li><strong>Check that JavaScript is enabled</strong></li>
                    <li><strong>Ensure cookies are enabled</strong></li>
                </ol>
            </div>

            <div class="solution">
                <h3>🔧 For Developers:</h3>
                <ul>
                    <li>Ensure form has <code class="code">{{{{ csrf_token }}}}</code> template tag</li>
                    <li>Check CSRF_TRUSTED_ORIGINS includes your domain</li>
                    <li>Verify CSRF middleware is enabled</li>
                    <li>Test with: <a href="/csrf-test/">CSRF Test Page</a></li>
                </ul>
            </div>

            <p><a href="javascript:history.back()">← Go Back</a> | <a href="/">🏠 Home</a></p>
        </div>
    </body>
    </html>
    """

    return HttpResponse(html, status=403)
