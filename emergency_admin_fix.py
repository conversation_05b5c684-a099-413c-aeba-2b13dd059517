#!/usr/bin/env python3
"""
EMERGENCY FIX: Create missing many-to-many tables for admin panel
This specifically fixes the "users_user_groups" and "users_user_user_permissions" tables
"""

import os
import sys

def setup_django():
    """Setup Django environment"""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'sqlplayground.settings')
    os.environ.setdefault('DEBUG', 'False')
    
    try:
        import django
        django.setup()
        print("✅ Django setup successful")
        return True
    except Exception as e:
        print(f"❌ Django setup failed: {e}")
        return False

def check_database_connection():
    """Check database connection"""
    print("🔍 Checking database connection...")
    
    try:
        from django.db import connection
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            if result:
                print("✅ Database connection successful")
                return True
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

def check_missing_admin_tables():
    """Check specifically for admin-related missing tables"""
    print("\n📋 Checking admin-related tables...")
    
    try:
        from django.db import connection
        
        critical_tables = [
            'users_user_groups',
            'users_user_user_permissions',
            'auth_group',
            'auth_permission',
            'django_content_type',
        ]
        
        missing = []
        
        with connection.cursor() as cursor:
            for table in critical_tables:
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_name = %s
                    )
                """, [table])
                
                exists = cursor.fetchone()[0]
                if exists:
                    print(f"✅ {table}")
                else:
                    print(f"❌ MISSING: {table}")
                    missing.append(table)
        
        return missing
        
    except Exception as e:
        print(f"❌ Table check failed: {e}")
        return []

def create_admin_critical_tables():
    """Create the critical tables needed for admin panel"""
    print("\n🔧 Creating critical admin tables...")
    
    try:
        from django.db import connection
        
        # SQL commands to create critical tables
        sql_commands = [
            # Content types (needed for permissions)
            """
            CREATE TABLE IF NOT EXISTS django_content_type (
                id BIGSERIAL PRIMARY KEY,
                app_label VARCHAR(100) NOT NULL,
                model VARCHAR(100) NOT NULL,
                UNIQUE(app_label, model)
            );
            """,
            
            # Auth group
            """
            CREATE TABLE IF NOT EXISTS auth_group (
                id BIGSERIAL PRIMARY KEY,
                name VARCHAR(150) UNIQUE NOT NULL
            );
            """,
            
            # Auth permission
            """
            CREATE TABLE IF NOT EXISTS auth_permission (
                id BIGSERIAL PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                content_type_id BIGINT,
                codename VARCHAR(100) NOT NULL
            );
            """,
            
            # Users user groups (THE CRITICAL MISSING TABLE)
            """
            CREATE TABLE IF NOT EXISTS users_user_groups (
                id BIGSERIAL PRIMARY KEY,
                user_id BIGINT NOT NULL,
                group_id BIGINT NOT NULL,
                UNIQUE(user_id, group_id)
            );
            CREATE INDEX IF NOT EXISTS users_user_groups_user_id_idx ON users_user_groups(user_id);
            CREATE INDEX IF NOT EXISTS users_user_groups_group_id_idx ON users_user_groups(group_id);
            """,
            
            # Users user permissions
            """
            CREATE TABLE IF NOT EXISTS users_user_user_permissions (
                id BIGSERIAL PRIMARY KEY,
                user_id BIGINT NOT NULL,
                permission_id BIGINT NOT NULL,
                UNIQUE(user_id, permission_id)
            );
            CREATE INDEX IF NOT EXISTS users_user_user_permissions_user_id_idx ON users_user_user_permissions(user_id);
            CREATE INDEX IF NOT EXISTS users_user_user_permissions_permission_id_idx ON users_user_user_permissions(permission_id);
            """,
            
            # Auth group permissions
            """
            CREATE TABLE IF NOT EXISTS auth_group_permissions (
                id BIGSERIAL PRIMARY KEY,
                group_id BIGINT NOT NULL,
                permission_id BIGINT NOT NULL,
                UNIQUE(group_id, permission_id)
            );
            """,
        ]
        
        with connection.cursor() as cursor:
            for i, sql in enumerate(sql_commands, 1):
                try:
                    cursor.execute(sql)
                    print(f"✅ Critical table {i}/{len(sql_commands)} created")
                except Exception as e:
                    print(f"⚠️  Table {i} warning: {e}")
        
        print("✅ All critical admin tables created")
        return True
        
    except Exception as e:
        print(f"❌ Critical table creation failed: {e}")
        return False

def test_admin_access():
    """Test if admin panel can now access user management"""
    print("\n🧪 Testing admin functionality...")
    
    try:
        from django.contrib.auth import get_user_model
        from django.contrib.auth.models import Group, Permission
        
        User = get_user_model()
        
        # Test basic queries
        user_count = User.objects.count()
        print(f"✅ Can query users: {user_count} users found")
        
        group_count = Group.objects.count()
        print(f"✅ Can query groups: {group_count} groups found")
        
        permission_count = Permission.objects.count()
        print(f"✅ Can query permissions: {permission_count} permissions found")
        
        # Test if we can access user groups (the problematic relationship)
        if user_count > 0:
            first_user = User.objects.first()
            user_groups = first_user.groups.count()
            print(f"✅ Can access user groups: {user_groups} groups for first user")
        
        return True
        
    except Exception as e:
        print(f"❌ Admin functionality test failed: {e}")
        return False

def populate_basic_content_types():
    """Populate basic content types needed for permissions"""
    print("\n📝 Populating basic content types...")
    
    try:
        from django.contrib.contenttypes.models import ContentType
        from django.apps import apps
        
        # Get or create content types for key models
        key_models = [
            ('users', 'user'),
            ('auth', 'group'),
            ('auth', 'permission'),
            ('admin', 'logentry'),
        ]
        
        for app_label, model_name in key_models:
            try:
                ct, created = ContentType.objects.get_or_create(
                    app_label=app_label,
                    model=model_name
                )
                if created:
                    print(f"✅ Created content type: {app_label}.{model_name}")
                else:
                    print(f"✅ Content type exists: {app_label}.{model_name}")
            except Exception as e:
                print(f"⚠️  Content type warning for {app_label}.{model_name}: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Content type population failed: {e}")
        return False

def main():
    """Main emergency fix function"""
    print("🚨 EMERGENCY ADMIN PANEL FIX")
    print("=" * 40)
    print("Fixing: relation 'users_user_groups' does not exist")
    
    # Setup Django
    if not setup_django():
        return
    
    # Check database connection
    if not check_database_connection():
        return
    
    # Check missing tables
    missing_tables = check_missing_admin_tables()
    
    if missing_tables:
        print(f"\n🔧 Found {len(missing_tables)} missing critical tables")
        
        # Create critical tables
        if create_admin_critical_tables():
            print("✅ Critical tables created")
            
            # Populate content types
            if populate_basic_content_types():
                print("✅ Content types populated")
            
            # Test admin functionality
            if test_admin_access():
                print("\n🎉 EMERGENCY FIX SUCCESSFUL!")
                print("\n📋 Next steps:")
                print("1. Restart your Python app in cPanel")
                print("2. Test admin panel: https://kodesql.in/admin/")
                print("3. Try deleting users in admin panel")
                print("4. User management should now work")
                
                print("\n⚠️  IMPORTANT:")
                print("- This is an emergency fix")
                print("- Run the full migration script later: python fix_all_missing_tables.py")
                print("- This ensures all tables are properly created")
            else:
                print("\n⚠️  Tables created but admin still has issues")
        else:
            print("\n❌ Failed to create critical tables")
    else:
        print("\n✅ All critical admin tables exist")
        
        # Still test functionality
        if test_admin_access():
            print("✅ Admin functionality working")
        else:
            print("⚠️  Admin has other issues - may need full migration")

if __name__ == "__main__":
    main()
