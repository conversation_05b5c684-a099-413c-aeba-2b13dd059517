# Django-native .htaccess for cPanel hosting
# Routes all requests through Django while serving static files efficiently

# Passenger Configuration
PassengerEnabled on
PassengerAppRoot /home/<USER>/public_html/KodeSQL
PassengerAppType wsgi
PassengerStartupFile passenger_wsgi.py
PassengerPython /home/<USER>/virtualenv/public_html/KodeSQL/3.10/bin/python

# Enable URL rewriting
RewriteEngine On

# Serve static files directly (Django-native approach with WhiteNoise fallback)
RewriteCond %{REQUEST_URI} ^/static/ [NC]
RewriteCond %{REQUEST_FILENAME} -f
RewriteRule ^static/(.*)$ - [L]

# Serve media files directly
RewriteCond %{REQUEST_URI} ^/media/ [NC]
RewriteCond %{REQUEST_FILENAME} -f
RewriteRule ^media/(.*)$ - [L]

# Route all other requests through Django
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} !^/static/
RewriteCond %{REQUEST_URI} !^/media/
RewriteRule ^(.*)$ /passenger_wsgi.py [QSA,L]

# Security headers (Django-native approach)
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
</IfModule>

# Protect sensitive files
<Files ".env">
    Require all denied
</Files>

<Files "*.log">
    Require all denied
</Files>

<Files "*.py">
    Require all denied
</Files>

# Allow passenger_wsgi.py to be executed
<Files "passenger_wsgi.py">
    Require all granted
</Files>

# Custom error pages (Django will handle these)
ErrorDocument 404 /404/
ErrorDocument 500 /500/
