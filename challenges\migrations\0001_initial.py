# Generated by Django 5.2.1 on 2025-07-05 11:38

import django.db.models.deletion
import django_ckeditor_5.fields
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Challenge",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("title", models.CharField(max_length=255)),
                ("description", django_ckeditor_5.fields.CKEditor5Field()),
                (
                    "difficulty",
                    models.CharField(
                        choices=[
                            ("easy", "Easy"),
                            ("medium", "Medium"),
                            ("hard", "Hard"),
                            ("extreme", "Extreme Hard"),
                        ],
                        default="easy",
                        max_length=10,
                    ),
                ),
                ("question", django_ckeditor_5.fields.CKEditor5<PERSON>ield()),
                ("hint", django_ckeditor_5.fields.CKEditor5Field(blank=True)),
                (
                    "schema_sql",
                    models.TextField(
                        blank=True,
                        help_text="SQL schema definition (CREATE TABLE statements) - will be auto-prefixed with challenge ID",
                    ),
                ),
                (
                    "run_dataset_sql",
                    models.TextField(
                        blank=True,
                        help_text="INSERT statements for test/trial data (users see this during testing)",
                    ),
                ),
                (
                    "submit_dataset_sql",
                    models.TextField(
                        blank=True,
                        help_text="INSERT statements for validation data (hidden test cases for final submission)",
                    ),
                ),
                (
                    "reference_query",
                    models.TextField(
                        blank=True,
                        help_text="The correct SQL query that solves this challenge (admin/superuser writes this)",
                    ),
                ),
                (
                    "mysql_schema_file",
                    models.FileField(
                        blank=True,
                        help_text="Upload MySQL schema file (.sql) containing table structures and data (legacy)",
                        null=True,
                        upload_to="challenges/schemas/",
                    ),
                ),
                ("expected_query", models.TextField(blank=True)),
                ("expected_result", models.JSONField(default=list)),
                (
                    "sample_data",
                    models.FileField(
                        blank=True,
                        help_text="Upload CSV or SQL file with sample data (legacy)",
                        null=True,
                        upload_to="challenges/sample_data/",
                    ),
                ),
                (
                    "database_schema_type",
                    models.CharField(
                        choices=[
                            ("employees", "Employee Database"),
                            ("ecommerce", "E-commerce Database (Orders & Returns)"),
                            (
                                "students",
                                "Student Database (Students, Courses, Enrollments)",
                            ),
                            ("custom", "Custom Schema"),
                        ],
                        default="employees",
                        help_text="Select a predefined database schema for this challenge",
                        max_length=20,
                    ),
                ),
                (
                    "custom_initialization_sql",
                    models.TextField(
                        blank=True,
                        help_text="Custom SQL commands (only used for custom schema type)",
                    ),
                ),
                (
                    "custom_database_schema",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="Custom database schema information (only used for custom schema type)",
                    ),
                ),
                (
                    "supported_engines",
                    models.JSONField(
                        blank=True,
                        default=list,
                        help_text="List of supported database engines for this challenge",
                    ),
                ),
                (
                    "subscription_type",
                    models.CharField(
                        choices=[("free", "Free"), ("paid", "Paid")],
                        default="free",
                        max_length=10,
                    ),
                ),
                (
                    "company",
                    models.CharField(
                        blank=True,
                        help_text="Company associated with this challenge",
                        max_length=100,
                    ),
                ),
                (
                    "tags",
                    models.JSONField(
                        default=list,
                        help_text="List of tags for categorization (e.g., ['joins', 'aggregation'])",
                    ),
                ),
                (
                    "xp",
                    models.PositiveIntegerField(
                        default=10,
                        help_text="Experience points awarded for completing this challenge",
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("order", models.PositiveIntegerField(default=0)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "ordering": ["order", "difficulty", "created_at"],
            },
        ),
        migrations.CreateModel(
            name="ChallengeSubscriptionPlan",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                (
                    "duration",
                    models.CharField(
                        choices=[
                            ("1_month", "1 Month"),
                            ("3_months", "3 Months"),
                            ("6_months", "6 Months"),
                            ("unlimited", "Unlimited"),
                        ],
                        max_length=20,
                    ),
                ),
                ("price", models.DecimalField(decimal_places=2, max_digits=10)),
                (
                    "original_price",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=10, null=True
                    ),
                ),
                ("description", models.TextField()),
                (
                    "features",
                    models.JSONField(
                        default=list, help_text="List of features for this plan"
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("is_recommended", models.BooleanField(default=False)),
                (
                    "sort_order",
                    models.IntegerField(
                        default=0, help_text="Lower numbers appear first"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "ordering": ["sort_order", "price"],
                "unique_together": {("duration",)},
            },
        ),
        migrations.CreateModel(
            name="UserChallengeSubscription",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("start_date", models.DateTimeField()),
                ("end_date", models.DateTimeField(blank=True, null=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("expired", "Expired"),
                            ("cancelled", "Cancelled"),
                            ("pending", "Pending Payment"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("amount_paid", models.DecimalField(decimal_places=2, max_digits=10)),
                (
                    "payment_reference",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "stripe_payment_intent_id",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "pending_expires_at",
                    models.DateTimeField(
                        blank=True, help_text="When pending payment expires", null=True
                    ),
                ),
                ("expiry_notification_sent", models.BooleanField(default=False)),
                ("final_notification_sent", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "plan",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="subscriptions",
                        to="challenges.challengesubscriptionplan",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="challenge_subscriptions",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="UserChallengeProgress",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("is_completed", models.BooleanField(default=False)),
                ("attempts", models.PositiveIntegerField(default=0)),
                ("completed_at", models.DateTimeField(blank=True, null=True)),
                ("best_query", models.TextField(blank=True)),
                (
                    "xp_earned",
                    models.PositiveIntegerField(
                        default=0, help_text="XP earned from completing this challenge"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "challenge",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="user_progress",
                        to="challenges.challenge",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="challenge_progress",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "unique_together": {("user", "challenge")},
            },
        ),
    ]
