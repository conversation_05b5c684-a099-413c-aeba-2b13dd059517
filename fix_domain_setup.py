#!/usr/bin/env python3
"""
Domain Setup Fix for cPanel Django Application
This script helps diagnose and fix domain configuration issues
"""

import os
import sys

def check_file_structure():
    """Check the current file structure"""
    print("🔍 Checking File Structure...")
    
    current_dir = os.getcwd()
    print(f"   Current directory: {current_dir}")
    
    # Check if we're in the right location
    expected_files = [
        'manage.py',
        'passenger_wsgi.py',
        'sqlplayground/settings.py',
        '.env'
    ]
    
    missing_files = []
    for file in expected_files:
        if os.path.exists(file):
            print(f"   ✅ {file} found")
        else:
            print(f"   ❌ {file} missing")
            missing_files.append(file)
    
    if missing_files:
        print(f"   ⚠️  Missing files: {missing_files}")
        return False
    
    print("   ✅ All essential files present")
    return True

def check_passenger_wsgi():
    """Check and fix passenger_wsgi.py"""
    print("🔍 Checking passenger_wsgi.py...")
    
    if not os.path.exists('passenger_wsgi.py'):
        print("   ❌ passenger_wsgi.py not found")
        return False
    
    with open('passenger_wsgi.py', 'r') as f:
        content = f.read()
    
    print(f"   Current passenger_wsgi.py content:")
    print(f"   {content[:200]}...")
    
    # Check if it has the correct Django setup
    if 'django' in content.lower() and 'application' in content:
        print("   ✅ passenger_wsgi.py looks correct")
        return True
    else:
        print("   ⚠️  passenger_wsgi.py might need updates")
        return False

def create_correct_passenger_wsgi():
    """Create a correct passenger_wsgi.py file"""
    print("🔧 Creating Correct passenger_wsgi.py...")
    
    passenger_content = '''#!/usr/bin/env python3
"""
Passenger WSGI file for Django application on cPanel
"""

import os
import sys

# Add the project directory to Python path
project_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_dir)

# Set Django settings module
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'sqlplayground.settings')

# Import Django and setup
try:
    import django
    django.setup()
    
    # Import the Django WSGI application
    from django.core.wsgi import get_wsgi_application
    application = get_wsgi_application()
    
    print("Django application loaded successfully")
    
except Exception as e:
    print(f"Error loading Django application: {e}")
    
    # Fallback application for debugging
    def application(environ, start_response):
        status = '500 Internal Server Error'
        headers = [('Content-type', 'text/plain')]
        start_response(status, headers)
        return [f"Django application failed to load: {str(e)}".encode()]
'''
    
    try:
        with open('passenger_wsgi.py', 'w') as f:
            f.write(passenger_content)
        
        # Make it executable
        os.chmod('passenger_wsgi.py', 0o755)
        
        print("   ✅ passenger_wsgi.py created successfully")
        return True
        
    except Exception as e:
        print(f"   ❌ Error creating passenger_wsgi.py: {e}")
        return False

def check_environment_variables():
    """Check if environment variables are being loaded"""
    print("🔍 Checking Environment Variables...")
    
    # Key environment variables from .htaccess
    key_vars = [
        'PRIMARY_DB_NAME',
        'PRIMARY_DB_USER', 
        'PRIMARY_DB_PASSWORD',
        'QUERY_MYSQL_DB_NAME',
        'QUERY_MYSQL_USER',
        'SECRET_KEY',
        'DEBUG'
    ]
    
    missing_vars = []
    for var in key_vars:
        value = os.environ.get(var)
        if value:
            if 'PASSWORD' in var or 'SECRET' in var:
                print(f"   ✅ {var}: [HIDDEN]")
            else:
                print(f"   ✅ {var}: {value}")
        else:
            print(f"   ❌ {var}: NOT SET")
            missing_vars.append(var)
    
    if missing_vars:
        print(f"   ⚠️  Missing environment variables: {missing_vars}")
        print("   💡 These should be set in your .htaccess file")
        return False
    
    print("   ✅ All key environment variables are set")
    return True

def test_django_import():
    """Test if Django can be imported and configured"""
    print("🔍 Testing Django Import...")
    
    try:
        # Set Django settings
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'sqlplayground.settings')
        
        import django
        django.setup()
        
        print("   ✅ Django imported and configured successfully")
        
        # Test database connection
        from django.db import connection
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
        
        print("   ✅ Database connection working")
        return True
        
    except Exception as e:
        print(f"   ❌ Django import/setup failed: {e}")
        return False

def create_debug_info_page():
    """Create a debug info page to help diagnose issues"""
    print("🔧 Creating Debug Info Page...")
    
    debug_content = '''#!/usr/bin/env python3
"""
Debug information page for Django application
Access this at: yourdomain.com/debug_info.py
"""

import os
import sys
import cgi

# Add project directory to path
project_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_dir)

def main():
    print("Content-Type: text/html\\n")
    print("<html><head><title>Django Debug Info</title></head><body>")
    print("<h1>Django Application Debug Information</h1>")
    
    print("<h2>Python Information</h2>")
    print(f"<p>Python Version: {sys.version}</p>")
    print(f"<p>Python Path: {sys.path}</p>")
    
    print("<h2>Environment Variables</h2>")
    print("<ul>")
    for key, value in os.environ.items():
        if 'PASSWORD' in key or 'SECRET' in key:
            print(f"<li>{key}: [HIDDEN]</li>")
        else:
            print(f"<li>{key}: {value}</li>")
    print("</ul>")
    
    print("<h2>File Structure</h2>")
    print("<ul>")
    for item in os.listdir('.'):
        print(f"<li>{item}</li>")
    print("</ul>")
    
    print("<h2>Django Test</h2>")
    try:
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'sqlplayground.settings')
        import django
        django.setup()
        print("<p style='color: green;'>✅ Django setup successful</p>")
        
        from django.db import connection
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
        print("<p style='color: green;'>✅ Database connection successful</p>")
        
    except Exception as e:
        print(f"<p style='color: red;'>❌ Django setup failed: {e}</p>")
    
    print("</body></html>")

if __name__ == "__main__":
    main()
'''
    
    try:
        with open('debug_info.py', 'w') as f:
            f.write(debug_content)
        
        os.chmod('debug_info.py', 0o755)
        
        print("   ✅ debug_info.py created")
        print("   💡 Access it at: https://kodesql.in/debug_info.py")
        return True
        
    except Exception as e:
        print(f"   ❌ Error creating debug_info.py: {e}")
        return False

def main():
    """Main function"""
    print("🚀 Domain Setup Fix for cPanel Django Application")
    print("="*60)
    
    # Run diagnostic steps
    steps = [
        ("Check File Structure", check_file_structure),
        ("Check passenger_wsgi.py", check_passenger_wsgi),
        ("Check Environment Variables", check_environment_variables),
        ("Test Django Import", test_django_import)
    ]
    
    issues_found = []
    
    for step_name, step_func in steps:
        print(f"\\n📋 {step_name}...")
        if not step_func():
            issues_found.append(step_name)
    
    # Apply fixes if needed
    if "Check passenger_wsgi.py" in issues_found:
        print("\\n🔧 Applying Fixes...")
        create_correct_passenger_wsgi()
    
    # Always create debug info page
    create_debug_info_page()
    
    print("\\n" + "="*60)
    print("📋 DOMAIN SETUP DIAGNOSIS COMPLETE")
    print("="*60)
    
    if issues_found:
        print(f"\\n⚠️  Issues found: {len(issues_found)}")
        for issue in issues_found:
            print(f"   - {issue}")
        
        print("\\n🔧 Recommended actions:")
        print("1. Check your .htaccess file environment variables")
        print("2. Ensure passenger_wsgi.py is correct (fixed above)")
        print("3. Check cPanel error logs")
        print("4. Access debug_info.py to see detailed information")
        
    else:
        print("\\n✅ No major issues found!")
        print("\\n🚀 Your domain should be working now")
    
    print("\\n💡 Next steps:")
    print("1. Access https://kodesql.in to test your domain")
    print("2. If still not working, access https://kodesql.in/debug_info.py")
    print("3. Check cPanel error logs for detailed error messages")

if __name__ == "__main__":
    main()
'''
