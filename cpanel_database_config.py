"""
cPanel Database Configuration Helper
This module provides database configuration specifically for cPanel hosting environments.
"""

import os
from django.conf import settings

class CPanelDatabaseConfig:
    """
    Helper class to manage database configurations for cPanel hosting.
    This addresses common issues with database connections in shared hosting environments.
    """
    
    @staticmethod
    def get_mysql_config():
        """
        Get MySQL configuration for cPanel environment.
        Returns configuration that works with PyMySQL driver.
        """
        return {
            'host': os.environ.get('QUERY_MYSQL_HOST', 'localhost'),
            'port': int(os.environ.get('QUERY_MYSQL_PORT', '3306')),
            'user': os.environ.get('QUERY_MYSQL_USER', 'kodesqli_mysql'),
            'password': os.environ.get('QUERY_MYSQL_PASSWORD', 'forgex99'),
            'database': os.environ.get('QUERY_MYSQL_DB_NAME', 'kodesqli_queries_mysql'),
            'charset': 'utf8mb4',
            'autocommit': True
        }
    
    @staticmethod
    def get_postgresql_config():
        """
        Get PostgreSQL configuration for cPanel environment.
        Returns configuration that works with psycopg2 driver.
        """
        return {
            'host': os.environ.get('QUERY_POSTGRES_HOST', 'localhost'),
            'port': int(os.environ.get('QUERY_POSTGRES_PORT', '5432')),
            'user': os.environ.get('QUERY_POSTGRES_USER', 'kodesqli_postgres'),
            'password': os.environ.get('QUERY_POSTGRES_PASSWORD', 'forgex99'),
            'database': os.environ.get('QUERY_POSTGRES_DB_NAME', 'kodesqli_queries_pg'),
            'connect_timeout': 10
        }
    
    @staticmethod
    def test_mysql_connection():
        """
        Test MySQL connection using PyMySQL.
        Returns True if connection successful, False otherwise.
        """
        try:
            import pymysql
            config = CPanelDatabaseConfig.get_mysql_config()
            
            conn = pymysql.connect(**config)
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            cursor.close()
            conn.close()
            return True
        except Exception as e:
            print(f"MySQL connection failed: {e}")
            return False
    
    @staticmethod
    def test_postgresql_connection():
        """
        Test PostgreSQL connection using psycopg2.
        Returns True if connection successful, False otherwise.
        """
        try:
            import psycopg2
            config = CPanelDatabaseConfig.get_postgresql_config()
            
            conn = psycopg2.connect(**config)
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            cursor.close()
            conn.close()
            return True
        except Exception as e:
            print(f"PostgreSQL connection failed: {e}")
            return False
    
    @staticmethod
    def get_mysql_connection():
        """
        Get a MySQL connection using PyMySQL with proper error handling.
        """
        try:
            import pymysql
            config = CPanelDatabaseConfig.get_mysql_config()
            return pymysql.connect(**config)
        except ImportError:
            raise ImportError("PyMySQL is required for MySQL connections. Install with: pip install PyMySQL")
        except Exception as e:
            raise ConnectionError(f"Failed to connect to MySQL: {e}")
    
    @staticmethod
    def get_postgresql_connection():
        """
        Get a PostgreSQL connection using psycopg2 with proper error handling.
        """
        try:
            import psycopg2
            from psycopg2.extras import RealDictCursor
            config = CPanelDatabaseConfig.get_postgresql_config()
            
            conn = psycopg2.connect(**config)
            return conn
        except ImportError:
            raise ImportError("psycopg2 is required for PostgreSQL connections. Install with: pip install psycopg2")
        except Exception as e:
            raise ConnectionError(f"Failed to connect to PostgreSQL: {e}")

def get_cpanel_database_settings():
    """
    Get database settings optimized for cPanel hosting.
    This function returns Django database configuration that works with cPanel.
    """
    return {
        'default': {
            'ENGINE': 'django.db.backends.postgresql',
            'NAME': os.environ.get('PRIMARY_DB_NAME', 'kodesqli_main_database'),
            'HOST': os.environ.get('PRIMARY_DB_HOST', 'localhost'),
            'PORT': os.environ.get('PRIMARY_DB_PORT', '5432'),
            'USER': os.environ.get('PRIMARY_DB_USER', 'kodesqli_postgres'),
            'PASSWORD': os.environ.get('PRIMARY_DB_PASSWORD', 'forgex99'),
            'OPTIONS': {
                'connect_timeout': 10,
            },
            'CONN_MAX_AGE': 60,
            'CONN_HEALTH_CHECKS': False,
        },
        'query_postgres': {
            'ENGINE': 'django.db.backends.postgresql',
            'NAME': os.environ.get('QUERY_POSTGRES_DB_NAME', 'kodesqli_queries_pg'),
            'HOST': os.environ.get('QUERY_POSTGRES_HOST', 'localhost'),
            'PORT': os.environ.get('QUERY_POSTGRES_PORT', '5432'),
            'USER': os.environ.get('QUERY_POSTGRES_USER', 'kodesqli_postgres'),
            'PASSWORD': os.environ.get('QUERY_POSTGRES_PASSWORD', 'forgex99'),
            'OPTIONS': {
                'connect_timeout': 10,
            },
            'CONN_MAX_AGE': 60,
            'CONN_HEALTH_CHECKS': False,
        },
        'query_mysql': {
            'ENGINE': 'django.db.backends.mysql',
            'NAME': os.environ.get('QUERY_MYSQL_DB_NAME', 'kodesqli_queries_mysql'),
            'HOST': os.environ.get('QUERY_MYSQL_HOST', 'localhost'),
            'PORT': os.environ.get('QUERY_MYSQL_PORT', '3306'),
            'USER': os.environ.get('QUERY_MYSQL_USER', 'kodesqli_mysql'),
            'PASSWORD': os.environ.get('QUERY_MYSQL_PASSWORD', 'forgex99'),
            'OPTIONS': {
                'charset': 'utf8mb4',
                'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
                'driver': 'pymysql',
            },
            'CONN_MAX_AGE': 60,
            'CONN_HEALTH_CHECKS': False,
        }
    }
