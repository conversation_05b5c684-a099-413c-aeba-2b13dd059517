#!/usr/bin/env python3
"""
Complete fix for Django 404 errors on cPanel
This script diagnoses and fixes URL routing issues
"""

import os
import sys
import django

def setup_django():
    """Setup Django environment"""
    sys.path.insert(0, os.getcwd())
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'sqlplayground.settings')
    django.setup()

def run_comprehensive_url_test():
    """Run comprehensive URL testing"""
    print("🔧 Running Comprehensive URL Tests...")
    
    try:
        from django.urls import resolve, reverse
        from django.test import Client
        from django.conf import settings
        
        client = Client()
        
        # Test URLs with actual HTTP requests
        test_urls = [
            ('/', 'Landing Page'),
            ('/dashboard/', 'Dashboard'),
            ('/challenges/', 'Challenges List'),
            ('/auth/login/', 'Login Page'),
            ('/auth/register/', 'Register Page'),
            ('/url-debug/', 'URL Debug Page'),
        ]
        
        print("\n📋 HTTP Request Tests:")
        for url, description in test_urls:
            try:
                response = client.get(url)
                status_class = "✅" if response.status_code in [200, 302] else "❌"
                print(f"{status_class} {url} ({description}) → {response.status_code}")
                
                # Check if it's a 404 with Django's custom handler
                if response.status_code == 404:
                    content = response.content.decode('utf-8')
                    if '404.html' in content or 'Page not found' in content:
                        print(f"   ⚠️  Django's custom 404 page detected")
                    
            except Exception as e:
                print(f"❌ {url} → ERROR: {e}")
        
        return True
    except Exception as e:
        print(f"❌ URL test failed: {e}")
        return False

def check_template_links():
    """Check if templates have correct links"""
    print("\n🔧 Checking Template Links...")
    
    template_files = [
        'templates/base.html',
        'templates/core/landing_page.html',
        'templates/core/dashboard.html',
    ]
    
    for template_file in template_files:
        if os.path.exists(template_file):
            print(f"📄 Checking {template_file}...")
            try:
                with open(template_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Look for common link patterns
                issues = []
                if 'href="/challenges"' in content:  # Missing trailing slash
                    issues.append("Missing trailing slash in challenges link")
                if 'href="/dashboard"' in content:  # Missing trailing slash
                    issues.append("Missing trailing slash in dashboard link")
                if 'action="/auth/login"' in content:  # Missing trailing slash
                    issues.append("Missing trailing slash in login form action")
                
                if issues:
                    print(f"   ⚠️  Issues found:")
                    for issue in issues:
                        print(f"      - {issue}")
                else:
                    print(f"   ✅ No obvious link issues found")
                    
            except Exception as e:
                print(f"   ❌ Error reading template: {e}")
        else:
            print(f"   ⚠️  Template not found: {template_file}")
    
    return True

def fix_common_url_issues():
    """Fix common URL configuration issues"""
    print("\n🔧 Checking for Common URL Issues...")
    
    try:
        from django.conf import settings
        
        # Check DEBUG setting
        if settings.DEBUG:
            print("⚠️  DEBUG=True in production - this might affect URL routing")
            print("   Consider setting DEBUG=False for production")
        else:
            print("✅ DEBUG=False (correct for production)")
        
        # Check ALLOWED_HOSTS
        allowed_hosts = settings.ALLOWED_HOSTS
        if 'kodesql.in' in allowed_hosts:
            print("✅ kodesql.in in ALLOWED_HOSTS")
        else:
            print("❌ kodesql.in not in ALLOWED_HOSTS")
        
        # Check ROOT_URLCONF
        root_urlconf = settings.ROOT_URLCONF
        print(f"📋 ROOT_URLCONF: {root_urlconf}")
        
        return True
    except Exception as e:
        print(f"❌ URL configuration check failed: {e}")
        return False

def create_simple_test_urls():
    """Create simple test URLs to verify routing"""
    print("\n🔧 Creating Simple Test URLs...")
    
    test_urls_content = '''
# Simple test URLs - add these to your main urls.py temporarily
from django.http import HttpResponse
from django.urls import path

def simple_test_view(request):
    return HttpResponse(f"""
    <h1>✅ Simple Test View Works!</h1>
    <p>Path: {request.path}</p>
    <p>Method: {request.method}</p>
    <p>If you see this, Django URL routing is working.</p>
    <hr>
    <p><a href="/">← Back to Home</a></p>
    <p><a href="/url-debug/">🐛 URL Debug</a></p>
    """)

# Add this to urlpatterns in sqlplayground/urls.py:
# path('simple-test/', simple_test_view, name='simple_test'),
'''
    
    try:
        with open('simple_test_urls.py', 'w') as f:
            f.write(test_urls_content)
        print("✅ Created simple_test_urls.py")
        print("💡 Add the test URL to your main urls.py to verify routing")
        return True
    except Exception as e:
        print(f"❌ Failed to create test URLs: {e}")
        return False

def check_static_files_interference():
    """Check if static files are interfering with URL routing"""
    print("\n🔧 Checking Static Files Configuration...")
    
    try:
        from django.conf import settings
        from django.contrib.staticfiles import finders
        
        print(f"STATIC_URL: {settings.STATIC_URL}")
        print(f"STATIC_ROOT: {settings.STATIC_ROOT}")
        
        # Check if static files directory exists
        if hasattr(settings, 'STATIC_ROOT') and settings.STATIC_ROOT:
            if os.path.exists(str(settings.STATIC_ROOT)):
                print("✅ STATIC_ROOT directory exists")
            else:
                print("⚠️  STATIC_ROOT directory doesn't exist")
                print("   Run: python manage.py collectstatic --noinput")
        
        return True
    except Exception as e:
        print(f"❌ Static files check failed: {e}")
        return False

def create_emergency_urls():
    """Create emergency URL configuration"""
    print("\n🔧 Creating Emergency URL Configuration...")
    
    emergency_urls = '''
# Emergency URL configuration - use if main URLs fail
from django.contrib import admin
from django.urls import path, include
from django.http import HttpResponse

def emergency_home(request):
    return HttpResponse("""
    <h1>🚨 Emergency Mode - KodeSQL</h1>
    <p>Basic URL routing is working!</p>
    <ul>
        <li><a href="/admin/">Admin Panel</a></li>
        <li><a href="/url-debug/">URL Debug</a></li>
        <li><a href="/debug-info/">Debug Info</a></li>
    </ul>
    <p>If you see this, Django is working but there might be issues with your app URLs.</p>
    """)

urlpatterns = [
    path('admin/', admin.site.urls),
    path('emergency/', emergency_home, name='emergency_home'),
    path('', include('core.urls')),  # Try to include core URLs
]
'''
    
    try:
        with open('emergency_urls.py', 'w') as f:
            f.write(emergency_urls)
        print("✅ Created emergency_urls.py")
        print("💡 To use: temporarily rename urls.py and use this as backup")
        return True
    except Exception as e:
        print(f"❌ Failed to create emergency URLs: {e}")
        return False

def main():
    """Main function to fix Django 404 errors"""
    print("🚀 Fixing Django 404 Errors - Complete Solution")
    print("=" * 60)
    
    # Setup Django
    try:
        setup_django()
        print("✅ Django setup successful")
    except Exception as e:
        print(f"❌ Django setup failed: {e}")
        return
    
    # Run all fixes and diagnostics
    fixes = [
        ("URL Configuration Check", fix_common_url_issues),
        ("Comprehensive URL Test", run_comprehensive_url_test),
        ("Template Links Check", check_template_links),
        ("Static Files Check", check_static_files_interference),
        ("Create Test URLs", create_simple_test_urls),
        ("Create Emergency URLs", create_emergency_urls),
    ]
    
    results = []
    for name, fix_func in fixes:
        print(f"\n--- {name} ---")
        try:
            result = fix_func()
            results.append((name, result))
        except Exception as e:
            print(f"❌ {name} failed: {e}")
            results.append((name, False))
    
    # Summary and recommendations
    print("\n" + "=" * 60)
    print("📋 DIAGNOSTIC RESULTS:")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for name, result in results:
        status = "✅" if result else "❌"
        print(f"{status} {name}")
    
    print(f"\nOverall: {passed}/{total} checks passed")
    
    print("\n🔧 IMMEDIATE STEPS TO FIX 404 ERRORS:")
    print("1. Test the URL debug page: https://kodesql.in/url-debug/")
    print("2. If that works, the issue is with template links or forms")
    print("3. If that fails, there's a Django configuration issue")
    print("4. Check cPanel error logs for specific errors")
    print("5. Restart Python application after any changes")
    
    print("\n💡 COMMON SOLUTIONS:")
    print("- Add trailing slashes to URLs in templates")
    print("- Check form action attributes")
    print("- Verify ALLOWED_HOSTS includes your domain")
    print("- Ensure all apps are in INSTALLED_APPS")
    print("- Run: python manage.py collectstatic --noinput")

if __name__ == "__main__":
    main()
