# Generated by Django 5.2.1 on 2025-07-09 15:56

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("challenges", "0005_razorpay_integration"),
    ]

    operations = [
        migrations.AlterUniqueTogether(
            name="challengesubscriptionplan",
            unique_together=set(),
        ),
        migrations.AlterField(
            model_name="challengesubscriptionplan",
            name="duration",
            field=models.CharField(
                choices=[
                    ("1_month", "1 Month"),
                    ("2_months", "2 Months"),
                    ("3_months", "3 Months"),
                    ("6_months", "6 Months"),
                    ("9_months", "9 Months"),
                    ("12_months", "12 Months"),
                    ("24_months", "24 Months"),
                    ("unlimited", "Unlimited"),
                ],
                max_length=20,
            ),
        ),
    ]
