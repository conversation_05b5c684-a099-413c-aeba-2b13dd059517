#!/usr/bin/env python3
"""
Fix Custom User Model Issues
This script fixes issues related to the custom User model
"""

import os
import sys
import django
from django.core.management import execute_from_command_line

def setup_django():
    """Setup Django environment"""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'sqlplayground.settings')
    django.setup()

def test_custom_user_model():
    """Test the custom User model"""
    print("🔍 Testing Custom User Model...")
    
    try:
        from django.contrib.auth import get_user_model
        User = get_user_model()
        
        print(f"   ✅ Custom User model: {User}")
        
        # Test basic operations
        user_count = User.objects.count()
        print(f"   ✅ User count: {user_count}")
        
        # Test superuser check
        superuser_count = User.objects.filter(is_superuser=True).count()
        print(f"   ✅ Superuser count: {superuser_count}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Custom User model test failed: {e}")
        return False

def create_superuser_if_needed():
    """Create a superuser if none exists"""
    print("🔧 Checking/Creating Superuser...")
    
    try:
        from django.contrib.auth import get_user_model
        User = get_user_model()
        
        superusers = User.objects.filter(is_superuser=True)
        if superusers.exists():
            print(f"   ✅ {superusers.count()} superuser(s) already exist")
            for user in superusers:
                print(f"      - {user.username} ({user.email})")
            return True
        else:
            print("   ⚠️  No superuser found")
            print("   💡 Creating a default superuser...")
            
            # Create a default superuser
            try:
                user = User.objects.create_superuser(
                    username='admin',
                    email='<EMAIL>',
                    password='KodeSQL@1995'  # Use the same password as your DB
                )
                print(f"   ✅ Superuser created: {user.username}")
                print("   💡 Username: admin, Password: KodeSQL@1995")
                return True
            except Exception as e:
                print(f"   ❌ Failed to create superuser: {e}")
                return False
            
    except Exception as e:
        print(f"   ❌ Superuser check failed: {e}")
        return False

def fix_static_files():
    """Fix static files collection"""
    print("🔧 Fixing Static Files...")
    
    try:
        from django.conf import settings
        
        static_root = settings.STATIC_ROOT
        print(f"   STATIC_ROOT: {static_root}")
        
        # Create static directory if it doesn't exist
        if not os.path.exists(static_root):
            print(f"   Creating static directory: {static_root}")
            os.makedirs(static_root, exist_ok=True)
            print("   ✅ Static directory created")
        
        # Run collectstatic
        print("   Running collectstatic...")
        execute_from_command_line(['manage.py', 'collectstatic', '--noinput'])
        print("   ✅ collectstatic completed successfully")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Static files fix failed: {e}")
        return False

def test_admin_access():
    """Test admin panel access"""
    print("🔍 Testing Admin Panel Access...")
    
    try:
        from django.urls import reverse
        from django.test import Client
        
        # Test admin URL
        admin_url = reverse('admin:index')
        print(f"   ✅ Admin URL: {admin_url}")
        
        # Test if admin can be accessed (basic test)
        client = Client()
        response = client.get(admin_url)
        
        if response.status_code in [200, 302]:  # 200 = OK, 302 = Redirect to login
            print(f"   ✅ Admin panel accessible (status: {response.status_code})")
            return True
        else:
            print(f"   ⚠️  Admin panel status: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Admin access test failed: {e}")
        return False

def create_site_configuration():
    """Create/update site configuration"""
    print("🔧 Setting Up Site Configuration...")
    
    try:
        from django.contrib.sites.models import Site
        
        # Update or create the default site
        site, created = Site.objects.get_or_create(
            id=1,
            defaults={
                'domain': 'kodesql.in',
                'name': 'KodeSQL'
            }
        )
        
        if not created:
            # Update existing site
            site.domain = 'kodesql.in'
            site.name = 'KodeSQL'
            site.save()
        
        print(f"   ✅ Site configured: {site.domain} ({site.name})")
        return True
        
    except Exception as e:
        print(f"   ❌ Site configuration failed: {e}")
        return False

def test_challenge_functionality():
    """Test if challenge-related functionality works"""
    print("🔍 Testing Challenge Functionality...")
    
    try:
        # Test if challenge models can be imported
        from challenges.models import Challenge
        challenge_count = Challenge.objects.count()
        print(f"   ✅ Challenge model working: {challenge_count} challenges")
        
        # Test challenge utils
        from challenges.utils import execute_dual_dataset_query
        print("   ✅ Challenge utils imported successfully")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Challenge functionality test failed: {e}")
        return False

def main():
    """Main function"""
    print("🚀 Fix Custom User Model and 500 Error Issues")
    print("="*60)
    
    # Setup Django
    try:
        setup_django()
        print("✅ Django setup successful")
    except Exception as e:
        print(f"❌ Django setup failed: {e}")
        return
    
    # Run fixes
    fixes = [
        ("Test Custom User Model", test_custom_user_model),
        ("Create Superuser", create_superuser_if_needed),
        ("Fix Static Files", fix_static_files),
        ("Test Admin Access", test_admin_access),
        ("Site Configuration", create_site_configuration),
        ("Test Challenge Functionality", test_challenge_functionality)
    ]
    
    failed_fixes = []
    
    for fix_name, fix_func in fixes:
        print(f"\n📋 {fix_name}...")
        if not fix_func():
            failed_fixes.append(fix_name)
    
    print("\n" + "="*60)
    print("📋 FIX RESULTS")
    print("="*60)
    
    if failed_fixes:
        print(f"\n⚠️  Issues remaining: {len(failed_fixes)}")
        for fix in failed_fixes:
            print(f"   - {fix}")
    else:
        print("\n✅ All fixes completed successfully!")
    
    print("\n🚀 Next steps:")
    print("1. Test your domain: https://kodesql.in")
    print("2. Access admin panel: https://kodesql.in/admin/")
    print("3. Login with: admin / KodeSQL@1995")
    print("4. Test challenge solve page")
    
    if failed_fixes:
        print("\n🔧 If issues persist:")
        print("1. Check cPanel error logs")
        print("2. Ensure all environment variables are correct")
        print("3. Verify file permissions")

if __name__ == "__main__":
    main()
