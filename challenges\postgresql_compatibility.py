"""
PostgreSQL 10.4 Compatibility Utilities
Ensures SQL queries and schema definitions work with PostgreSQL 10.4
"""

import re
from typing import Dict, List, Tuple


class PostgreSQL104Compatibility:
    """
    Utility class to ensure PostgreSQL 10.4 compatibility.
    Handles SQL syntax that might not be supported in older PostgreSQL versions.
    """
    
    def __init__(self):
        # PostgreSQL 10.4 compatible data type mappings
        self.type_mappings = {
            # Ensure we use compatible data types
            'BIGSERIAL': 'SERIAL',  # BIGSERIAL is supported but SERIAL is more compatible
            'JSONB': 'JSON',  # JSONB is supported in 10.4, but JSON is more universal
        }
        
        # Functions that might need compatibility adjustments
        self.function_mappings = {
            # No specific function mappings needed for 10.4
        }
    
    def adapt_schema_sql(self, sql: str) -> str:
        """
        Adapt schema SQL for PostgreSQL 10.4 compatibility.
        
        Args:
            sql: Original SQL schema definition
            
        Returns:
            PostgreSQL 10.4 compatible SQL
        """
        adapted_sql = sql
        
        # Apply data type mappings
        for old_type, new_type in self.type_mappings.items():
            adapted_sql = re.sub(
                rf'\b{old_type}\b', 
                new_type, 
                adapted_sql, 
                flags=re.IGNORECASE
            )
        
        # Ensure SERIAL columns are properly defined
        adapted_sql = self._fix_serial_columns(adapted_sql)
        
        # Remove any PostgreSQL 11+ specific syntax
        adapted_sql = self._remove_unsupported_syntax(adapted_sql)
        
        return adapted_sql
    
    def adapt_query_sql(self, sql: str) -> str:
        """
        Adapt query SQL for PostgreSQL 10.4 compatibility.
        
        Args:
            sql: Original SQL query
            
        Returns:
            PostgreSQL 10.4 compatible SQL query
        """
        adapted_sql = sql
        
        # Apply function mappings if needed
        for old_func, new_func in self.function_mappings.items():
            adapted_sql = re.sub(
                rf'\b{old_func}\b', 
                new_func, 
                adapted_sql, 
                flags=re.IGNORECASE
            )
        
        # Ensure window functions are properly formatted
        adapted_sql = self._fix_window_functions(adapted_sql)
        
        return adapted_sql
    
    def _fix_serial_columns(self, sql: str) -> str:
        """
        Ensure SERIAL columns are properly defined for PostgreSQL 10.4.
        """
        # SERIAL is well supported in PostgreSQL 10.4, no changes needed
        return sql
    
    def _remove_unsupported_syntax(self, sql: str) -> str:
        """
        Remove PostgreSQL 11+ specific syntax that's not supported in 10.4.
        """
        # PostgreSQL 10.4 is quite feature-complete, most syntax is supported
        # Remove any GENERATED columns (PostgreSQL 12+ feature)
        sql = re.sub(
            r'\s+GENERATED\s+ALWAYS\s+AS\s+\([^)]+\)\s+STORED',
            '',
            sql,
            flags=re.IGNORECASE
        )
        
        return sql
    
    def _fix_window_functions(self, sql: str) -> str:
        """
        Ensure window functions are compatible with PostgreSQL 10.4.
        Window functions are well supported in 10.4, no changes needed.
        """
        return sql
    
    def validate_compatibility(self, sql: str) -> Tuple[bool, List[str]]:
        """
        Validate SQL for PostgreSQL 10.4 compatibility.
        
        Args:
            sql: SQL to validate
            
        Returns:
            Tuple of (is_compatible, list_of_issues)
        """
        issues = []
        
        # Check for PostgreSQL 11+ features
        if re.search(r'\bGENERATED\s+ALWAYS\s+AS\b', sql, re.IGNORECASE):
            issues.append("GENERATED columns are not supported in PostgreSQL 10.4")
        
        # Check for PostgreSQL 12+ features
        if re.search(r'\bCOPY\s+FROM\s+PROGRAM\b', sql, re.IGNORECASE):
            issues.append("COPY FROM PROGRAM is not supported in PostgreSQL 10.4")
        
        # Check for PostgreSQL 13+ features
        if re.search(r'\bEXTENDED\s+STATISTICS\b', sql, re.IGNORECASE):
            issues.append("Extended statistics syntax may not be fully supported in PostgreSQL 10.4")
        
        return len(issues) == 0, issues
    
    def get_connection_options(self) -> Dict[str, str]:
        """
        Get PostgreSQL 10.4 compatible connection options.
        
        Returns:
            Dictionary of connection options
        """
        return {
            'options': '-c default_transaction_isolation=read_committed',
            'connect_timeout': '10',
            'application_name': 'django_sql_playground',
        }


# Global instance for easy access
pg104_compat = PostgreSQL104Compatibility()


def adapt_sql_for_pg104(sql: str, is_schema: bool = False) -> str:
    """
    Convenience function to adapt SQL for PostgreSQL 10.4.
    
    Args:
        sql: SQL to adapt
        is_schema: Whether this is schema SQL (True) or query SQL (False)
        
    Returns:
        PostgreSQL 10.4 compatible SQL
    """
    if is_schema:
        return pg104_compat.adapt_schema_sql(sql)
    else:
        return pg104_compat.adapt_query_sql(sql)


def validate_pg104_compatibility(sql: str) -> Tuple[bool, List[str]]:
    """
    Convenience function to validate PostgreSQL 10.4 compatibility.
    
    Args:
        sql: SQL to validate
        
    Returns:
        Tuple of (is_compatible, list_of_issues)
    """
    return pg104_compat.validate_compatibility(sql)
