# Optimized .htaccess for cPanel Django hosting
# Use this if the main .htaccess doesn't work

# Passenger Configuration for cPanel
PassengerEnabled on
PassengerAppRoot /home/<USER>/public_html/KodeSQL
PassengerAppType wsgi
PassengerStartupFile passenger_wsgi.py
PassengerPython /home/<USER>/virtualenv/public_html/KodeSQL/3.10/bin/python

# Force all requests through Django except static files
RewriteEngine On

# Handle static files directly
RewriteCond %{REQUEST_URI} ^/static/ [NC]
RewriteRule ^static/(.*)$ /home/<USER>/public_html/static/$1 [L]

# Handle media files directly  
RewriteCond %{REQUEST_URI} ^/media/ [NC]
RewriteRule ^media/(.*)$ /home/<USER>/public_html/media/$1 [L]

# Handle admin static files
RewriteCond %{REQUEST_URI} ^/admin/static/ [NC]
RewriteRule ^admin/static/(.*)$ /home/<USER>/public_html/static/admin/$1 [L]

# Route everything else through Django
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} !^/static/
RewriteCond %{REQUEST_URI} !^/media/
RewriteRule ^(.*)$ /passenger_wsgi.py [QSA,L]

# Alternative routing (if above doesn't work)
# RewriteRule ^(.*)$ / [QSA,L]

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# Error documents
ErrorDocument 404 /404/
ErrorDocument 500 /500/

# File protection
<Files "*.py">
    Require all denied
</Files>

<Files ".env">
    Require all denied
</Files>

<Files "*.log">
    Require all denied
</Files>

<Files "passenger_wsgi.py">
    Require all granted
</Files>

# Compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Cache control for static files
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/ico "access plus 1 month"
</IfModule>
