#!/usr/bin/env python3
"""
Test CSRF settings without database connection
"""

import os
import sys

def test_csrf_settings():
    """Test CSRF settings in Django configuration"""
    print("🔍 Testing CSRF Settings...")
    
    try:
        # Add current directory to Python path
        sys.path.insert(0, os.getcwd())
        
        # Import settings without Django setup
        import importlib.util
        spec = importlib.util.spec_from_file_location("settings", "sqlplayground/settings.py")
        settings = importlib.util.module_from_spec(spec)
        
        # Mock os.environ for settings
        os.environ.setdefault('DEBUG', 'False')
        
        spec.loader.exec_module(settings)
        
        # Check CSRF settings
        csrf_settings = {
            'CSRF_COOKIE_HTTPONLY': getattr(settings, 'CSRF_COOKIE_HTTPONLY', 'Not set'),
            'CSRF_COOKIE_SAMESITE': getattr(settings, 'CSRF_COOKIE_SAMESITE', 'Not set'),
            'CSRF_COOKIE_SECURE': getattr(settings, 'CSRF_COOKIE_SECURE', 'Not set'),
            'CSRF_USE_SESSIONS': getattr(settings, 'CSRF_USE_SESSIONS', 'Not set'),
            'CSRF_TRUSTED_ORIGINS': getattr(settings, 'CSRF_TRUSTED_ORIGINS', 'Not set'),
            'DEBUG': getattr(settings, 'DEBUG', 'Not set'),
        }
        
        print("📋 CSRF Settings:")
        for key, value in csrf_settings.items():
            status = "✅" if value != 'Not set' else "❌"
            print(f"   {status} {key}: {value}")
        
        # Check middleware
        middleware = getattr(settings, 'MIDDLEWARE', [])
        csrf_middleware = 'django.middleware.csrf.CsrfViewMiddleware'
        session_middleware = 'django.contrib.sessions.middleware.SessionMiddleware'
        
        if csrf_middleware in middleware:
            print(f"✅ CSRF middleware enabled (position {middleware.index(csrf_middleware) + 1})")
        else:
            print(f"❌ CSRF middleware missing")
        
        if session_middleware in middleware:
            print(f"✅ Session middleware enabled (position {middleware.index(session_middleware) + 1})")
        else:
            print(f"❌ Session middleware missing")
        
        # Check optimal settings
        print("\n🔧 Settings Analysis:")
        
        if getattr(settings, 'CSRF_COOKIE_HTTPONLY', True) == False:
            print("✅ CSRF_COOKIE_HTTPONLY = False (correct for JavaScript access)")
        else:
            print("⚠️  CSRF_COOKIE_HTTPONLY should be False for JavaScript access")
        
        if getattr(settings, 'CSRF_COOKIE_SAMESITE', '') == 'Lax':
            print("✅ CSRF_COOKIE_SAMESITE = 'Lax' (correct)")
        else:
            print("⚠️  CSRF_COOKIE_SAMESITE should be 'Lax'")
        
        if getattr(settings, 'CSRF_USE_SESSIONS', True) == False:
            print("✅ CSRF_USE_SESSIONS = False (correct for cookies)")
        else:
            print("⚠️  CSRF_USE_SESSIONS should be False")
        
        trusted_origins = getattr(settings, 'CSRF_TRUSTED_ORIGINS', [])
        if 'https://kodesql.in' in trusted_origins:
            print("✅ kodesql.in in CSRF_TRUSTED_ORIGINS")
        else:
            print("❌ kodesql.in missing from CSRF_TRUSTED_ORIGINS")
        
        return True
        
    except Exception as e:
        print(f"❌ Settings test failed: {e}")
        return False

def check_template_csrf():
    """Check if signup template has CSRF token"""
    print("\n📄 Checking Signup Template...")
    
    try:
        template_path = 'templates/users/register.html'
        
        if os.path.exists(template_path):
            with open(template_path, 'r') as f:
                content = f.read()
            
            if '{% csrf_token %}' in content:
                print("✅ CSRF token found in signup template")
                
                # Check form method
                if 'method="post"' in content.lower():
                    print("✅ Form uses POST method")
                else:
                    print("⚠️  Form method not found or not POST")
                
                # Check form action
                if 'action=' in content:
                    print("✅ Form has action attribute")
                else:
                    print("✅ Form uses default action (current URL)")
                
                return True
            else:
                print("❌ CSRF token missing from signup template")
                return False
        else:
            print("❌ Signup template not found")
            return False
            
    except Exception as e:
        print(f"❌ Template check failed: {e}")
        return False

def check_htaccess_csrf():
    """Check if .htaccess might interfere with CSRF"""
    print("\n🔧 Checking .htaccess Configuration...")
    
    try:
        if os.path.exists('.htaccess'):
            with open('.htaccess', 'r') as f:
                content = f.read()
            
            # Check for problematic rules
            issues = []
            
            if 'RewriteRule' in content and 'POST' in content:
                issues.append("POST requests might be affected by rewrite rules")
            
            if 'Header' in content and 'Cookie' in content:
                issues.append("Cookie headers might be modified")
            
            if issues:
                print("⚠️  Potential .htaccess issues:")
                for issue in issues:
                    print(f"   - {issue}")
            else:
                print("✅ .htaccess looks good for CSRF")
            
            return True
        else:
            print("⚠️  .htaccess file not found")
            return False
            
    except Exception as e:
        print(f"❌ .htaccess check failed: {e}")
        return False

def main():
    """Main function"""
    print("🚀 CSRF Settings Test (No Database Required)")
    print("=" * 50)
    
    tests = [
        ("CSRF Settings", test_csrf_settings),
        ("Template CSRF Token", check_template_csrf),
        (".htaccess Configuration", check_htaccess_csrf),
    ]
    
    results = []
    for name, test_func in tests:
        print(f"\n--- {name} ---")
        try:
            result = test_func()
            results.append((name, result))
        except Exception as e:
            print(f"❌ {name} failed: {e}")
            results.append((name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 TEST RESULTS:")
    
    all_passed = True
    for name, result in results:
        status = "✅" if result else "❌"
        print(f"{status} {name}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 CSRF configuration looks good!")
    else:
        print("\n⚠️  Some issues found - check above for details")
    
    print("\n🔧 Next steps:")
    print("1. Upload updated files to cPanel")
    print("2. Restart Python application")
    print("3. Clear browser cookies and cache")
    print("4. Test signup at: https://kodesql.in/auth/register/")
    print("5. If still failing, test: https://kodesql.in/csrf-test/")

if __name__ == "__main__":
    main()
