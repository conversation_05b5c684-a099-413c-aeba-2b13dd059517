# Django-focused .htaccess for cPanel
# Minimal Apache configuration, let Django handle everything

# Enable Passenger for Python
PassengerEnabled on
PassengerAppRoot /home/<USER>/public_html/KodeSQL
PassengerPython /home/<USER>/virtualenv/public_html/KodeSQL/3.10/bin/python
PassengerAppType wsgi
PassengerStartupFile passenger_wsgi.py

# Basic security
<Files ".env">
    Require all denied
</Files>

<Files "*.log">
    Require all denied
</Files>

# Allow passenger_wsgi.py to be executed
<Files "passenger_wsgi.py">
    Require all granted
</Files>

# Protect other Python files from direct access
<FilesMatch "\.py$">
    <RequireAll>
        Require all denied
        Require file passenger_wsgi.py
    </RequireAll>
</FilesMatch>

# No URL rewriting - let Django handle all routing through its urls.py
