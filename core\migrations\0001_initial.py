# Generated by Django 5.2.1 on 2025-07-05 11:38

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="SiteSettings",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "site_name",
                    models.Char<PERSON>ield(default="SQL Playground", max_length=255),
                ),
                (
                    "site_description",
                    models.TextField(default="Interactive SQL learning platform"),
                ),
                ("maintenance_mode", models.BooleanField(default=False)),
                ("allow_registration", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "Site Settings",
                "verbose_name_plural": "Site Settings",
            },
        ),
    ]
