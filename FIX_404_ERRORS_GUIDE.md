# 🔧 Fix 404 Errors on cPanel - Complete Guide

## 🚨 Problem: Landing Page Loads, All Other URLs Give 404

This is a common cPanel hosting issue where the landing page loads but all navigation links and buttons give 404 errors. The problem is that Apache isn't properly routing requests through Django.

## 🔍 Root Cause Analysis

The issue is typically caused by:
1. **Incorrect .htaccess configuration** - Not routing requests through Django
2. **Passenger configuration issues** - Python app not properly set up
3. **Static file conflicts** - Apache serving files directly instead of through Django
4. **URL rewriting problems** - Requests not reaching Django's URL dispatcher

## ✅ Step-by-Step Fix

### Step 1: Test Django Routing
First, let's verify if Django itself is working:

1. **Upload the debug files** to your cPanel
2. **Test the debug URL**: `https://kodesql.in/debug-routing/`
3. If this works, Django is fine - the issue is with .htaccess

### Step 2: Fix .htaccess Configuration

Try these .htaccess configurations in order:

#### Option A: Use the Optimized .htaccess
```bash
# In cPanel File Manager or SSH:
mv .htaccess .htaccess_backup
mv .htaccess_cpanel_optimized .htaccess
```

#### Option B: Use Simple .htaccess (if Option A doesn't work)
```bash
mv .htaccess .htaccess_backup2
mv .htaccess_simple .htaccess
```

### Step 3: Restart Python Application
- Go to cPanel → Python App
- Click "Restart" for your KodeSQL application
- Wait 30 seconds for restart to complete

### Step 4: Test URLs
Test these URLs in order:
1. `https://kodesql.in/` (should work - landing page)
2. `https://kodesql.in/debug-routing/` (test Django routing)
3. `https://kodesql.in/dashboard/` (test navigation)
4. `https://kodesql.in/auth/login/` (test authentication)

## 🔧 Alternative Solutions

### If .htaccess fixes don't work:

#### Solution 1: Check cPanel Python App Configuration
1. Go to cPanel → Python App
2. Verify these settings:
   - **Python version**: 3.10
   - **Application root**: `/home/<USER>/public_html/KodeSQL`
   - **Application URL**: `/` (or your subdirectory)
   - **Application startup file**: `passenger_wsgi.py`

#### Solution 2: Check File Permissions
```bash
chmod 755 passenger_wsgi.py
chmod 644 .htaccess
chmod -R 755 sqlplayground/
```

#### Solution 3: Use Subdirectory Installation
If root installation doesn't work, try installing in a subdirectory:
1. Move files to `/home/<USER>/public_html/app/`
2. Update .htaccess paths accordingly
3. Access via `https://kodesql.in/app/`

## 🐛 Debugging Steps

### Check Error Logs
1. cPanel → Error Logs
2. Look for recent errors when accessing URLs
3. Common errors to look for:
   - "File not found"
   - "No such file or directory"
   - "Permission denied"
   - "Internal server error"

### Test with Simple HTML
Create a test file to verify basic routing:
```html
<!-- test.html -->
<h1>Test Page</h1>
<p>If you see this, basic file serving works.</p>
```

### Run Diagnostic Script
```bash
python fix_404_errors_cpanel.py
```

## 📋 Common Issues & Solutions

| Issue | Symptom | Solution |
|-------|---------|----------|
| .htaccess not working | All URLs 404 except landing | Try alternative .htaccess |
| Passenger not configured | 500 errors | Check Python App settings |
| Wrong Python path | Import errors | Verify virtual environment path |
| File permissions | Permission denied | Set correct chmod permissions |
| Static files conflict | CSS/JS not loading | Update static file paths |

## 🎯 Quick Test Checklist

- [ ] Landing page loads: `https://kodesql.in/`
- [ ] Debug routing works: `https://kodesql.in/debug-routing/`
- [ ] Dashboard loads: `https://kodesql.in/dashboard/`
- [ ] Login page loads: `https://kodesql.in/auth/login/`
- [ ] Admin panel loads: `https://kodesql.in/admin/`
- [ ] Static files load (CSS/JS)
- [ ] No 404 errors in navigation

## 🆘 Emergency Fallback

If nothing works, use this minimal configuration:

### Minimal .htaccess
```apache
PassengerEnabled on
PassengerAppRoot /home/<USER>/public_html/KodeSQL
PassengerPython /home/<USER>/virtualenv/public_html/KodeSQL/3.10/bin/python
```

### Minimal passenger_wsgi.py
```python
import os
import sys
sys.path.insert(0, '/home/<USER>/public_html/KodeSQL')
os.environ['DJANGO_SETTINGS_MODULE'] = 'sqlplayground.settings'
from django.core.wsgi import get_wsgi_application
application = get_wsgi_application()
```

## 📞 Next Steps

1. **Try the fixes in order** (don't skip steps)
2. **Test after each change**
3. **Check error logs** if issues persist
4. **Contact hosting support** if all else fails

The 404 errors should be resolved after applying these fixes! 🎉
