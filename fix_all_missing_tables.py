#!/usr/bin/env python3
"""
Comprehensive fix for all missing database tables
This script creates all missing Django tables including many-to-many relationships
"""

import os
import sys

def setup_django():
    """Setup Django environment"""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'sqlplayground.settings')
    os.environ.setdefault('DEBUG', 'False')
    
    try:
        import django
        django.setup()
        print("✅ Django setup successful")
        return True
    except Exception as e:
        print(f"❌ Django setup failed: {e}")
        return False

def check_database_connection():
    """Check database connection"""
    print("🔍 Checking database connection...")
    
    try:
        from django.db import connection
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            if result:
                print("✅ Database connection successful")
                return True
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

def get_missing_tables():
    """Check which tables are missing"""
    print("\n📋 Checking for missing tables...")
    
    try:
        from django.db import connection
        from django.apps import apps
        
        missing_tables = []
        
        with connection.cursor() as cursor:
            # Get all existing tables
            cursor.execute("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public'
            """)
            existing_tables = {row[0] for row in cursor.fetchall()}
            
            # Check core Django tables
            required_tables = [
                'django_migrations',
                'django_content_type',
                'django_session',
                'auth_permission',
                'auth_group',
                'auth_group_permissions',
                'users_user',
                'users_user_groups',
                'users_user_user_permissions',
                'users_userprofile',
                'users_userdatabase',
                'django_admin_log',
                'django_site',
            ]
            
            for table in required_tables:
                if table not in existing_tables:
                    missing_tables.append(table)
                    print(f"❌ Missing: {table}")
                else:
                    print(f"✅ Exists: {table}")
            
            return missing_tables, existing_tables
            
    except Exception as e:
        print(f"❌ Table check failed: {e}")
        return [], set()

def create_missing_core_tables():
    """Create missing core Django tables"""
    print("\n🔧 Creating missing core tables...")
    
    try:
        from django.db import connection
        
        sql_commands = [
            # Django migrations table
            """
            CREATE TABLE IF NOT EXISTS django_migrations (
                id BIGSERIAL PRIMARY KEY,
                app VARCHAR(255) NOT NULL,
                name VARCHAR(255) NOT NULL,
                applied TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                UNIQUE(app, name)
            );
            """,
            
            # Django content types
            """
            CREATE TABLE IF NOT EXISTS django_content_type (
                id BIGSERIAL PRIMARY KEY,
                app_label VARCHAR(100) NOT NULL,
                model VARCHAR(100) NOT NULL,
                UNIQUE(app_label, model)
            );
            """,
            
            # Django sessions
            """
            CREATE TABLE IF NOT EXISTS django_session (
                session_key VARCHAR(40) PRIMARY KEY,
                session_data TEXT NOT NULL,
                expire_date TIMESTAMP WITH TIME ZONE NOT NULL
            );
            CREATE INDEX IF NOT EXISTS django_session_expire_date_idx ON django_session(expire_date);
            """,
            
            # Auth permission
            """
            CREATE TABLE IF NOT EXISTS auth_permission (
                id BIGSERIAL PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                content_type_id BIGINT NOT NULL,
                codename VARCHAR(100) NOT NULL,
                UNIQUE(content_type_id, codename)
            );
            """,
            
            # Auth group
            """
            CREATE TABLE IF NOT EXISTS auth_group (
                id BIGSERIAL PRIMARY KEY,
                name VARCHAR(150) UNIQUE NOT NULL
            );
            """,
            
            # Auth group permissions
            """
            CREATE TABLE IF NOT EXISTS auth_group_permissions (
                id BIGSERIAL PRIMARY KEY,
                group_id BIGINT NOT NULL,
                permission_id BIGINT NOT NULL,
                UNIQUE(group_id, permission_id)
            );
            """,
            
            # Django admin log
            """
            CREATE TABLE IF NOT EXISTS django_admin_log (
                id BIGSERIAL PRIMARY KEY,
                action_time TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                object_id TEXT,
                object_repr VARCHAR(200) NOT NULL,
                action_flag SMALLINT NOT NULL,
                change_message TEXT NOT NULL,
                content_type_id BIGINT,
                user_id BIGINT NOT NULL
            );
            """,
            
            # Django site
            """
            CREATE TABLE IF NOT EXISTS django_site (
                id BIGSERIAL PRIMARY KEY,
                domain VARCHAR(100) NOT NULL UNIQUE,
                name VARCHAR(50) NOT NULL
            );
            INSERT INTO django_site (id, domain, name) VALUES (1, 'kodesql.in', 'KodeSQL') ON CONFLICT (id) DO NOTHING;
            """,
        ]
        
        with connection.cursor() as cursor:
            for sql in sql_commands:
                try:
                    cursor.execute(sql)
                    print("✅ Core table created/verified")
                except Exception as e:
                    print(f"⚠️  Core table creation warning: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Core table creation failed: {e}")
        return False

def create_user_model_tables():
    """Create custom user model tables and relationships"""
    print("\n🔧 Creating user model tables...")

    try:
        from django.db import connection

        # First, ensure auth_group and auth_permission exist
        auth_tables = [
            """
            CREATE TABLE IF NOT EXISTS auth_group (
                id BIGSERIAL PRIMARY KEY,
                name VARCHAR(150) UNIQUE NOT NULL
            );
            """,
            """
            CREATE TABLE IF NOT EXISTS auth_permission (
                id BIGSERIAL PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                content_type_id BIGINT NOT NULL,
                codename VARCHAR(100) NOT NULL,
                UNIQUE(content_type_id, codename)
            );
            """,
        ]

        user_tables = [
            # Users user table
            """
            CREATE TABLE IF NOT EXISTS users_user (
                id BIGSERIAL PRIMARY KEY,
                password VARCHAR(128) NOT NULL,
                last_login TIMESTAMP WITH TIME ZONE,
                is_superuser BOOLEAN NOT NULL DEFAULT FALSE,
                username VARCHAR(150) UNIQUE NOT NULL,
                first_name VARCHAR(150) NOT NULL DEFAULT '',
                last_name VARCHAR(150) NOT NULL DEFAULT '',
                email VARCHAR(254) UNIQUE NOT NULL,
                is_staff BOOLEAN NOT NULL DEFAULT FALSE,
                is_active BOOLEAN NOT NULL DEFAULT TRUE,
                date_joined TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                is_email_verified BOOLEAN NOT NULL DEFAULT FALSE,
                created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
            );
            """,
        ]

        # Many-to-many relationship tables (these are causing the admin errors)
        m2m_tables = [
            # Users user groups (many-to-many) - THIS IS THE MISSING TABLE
            """
            CREATE TABLE IF NOT EXISTS users_user_groups (
                id BIGSERIAL PRIMARY KEY,
                user_id BIGINT NOT NULL,
                group_id BIGINT NOT NULL,
                UNIQUE(user_id, group_id)
            );
            CREATE INDEX IF NOT EXISTS users_user_groups_user_id_idx ON users_user_groups(user_id);
            CREATE INDEX IF NOT EXISTS users_user_groups_group_id_idx ON users_user_groups(group_id);
            """,

            # Users user permissions (many-to-many)
            """
            CREATE TABLE IF NOT EXISTS users_user_user_permissions (
                id BIGSERIAL PRIMARY KEY,
                user_id BIGINT NOT NULL,
                permission_id BIGINT NOT NULL,
                UNIQUE(user_id, permission_id)
            );
            CREATE INDEX IF NOT EXISTS users_user_user_permissions_user_id_idx ON users_user_user_permissions(user_id);
            CREATE INDEX IF NOT EXISTS users_user_user_permissions_permission_id_idx ON users_user_user_permissions(permission_id);
            """,
        ]

        other_tables = [
            # User profile
            """
            CREATE TABLE IF NOT EXISTS users_userprofile (
                id BIGSERIAL PRIMARY KEY,
                bio TEXT NOT NULL DEFAULT '',
                location VARCHAR(100) NOT NULL DEFAULT '',
                birth_date DATE,
                profile_picture VARCHAR(100) NOT NULL DEFAULT '',
                theme_preference VARCHAR(10) NOT NULL DEFAULT 'light',
                total_xp INTEGER NOT NULL DEFAULT 0,
                created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                user_id BIGINT NOT NULL UNIQUE REFERENCES users_user(id) ON DELETE CASCADE
            );
            """,

            # User database
            """
            CREATE TABLE IF NOT EXISTS users_userdatabase (
                id BIGSERIAL PRIMARY KEY,
                database_path VARCHAR(255) NOT NULL,
                created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                last_accessed TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                user_id BIGINT NOT NULL UNIQUE REFERENCES users_user(id) ON DELETE CASCADE
            );
            """,
        ]

        with connection.cursor() as cursor:
            # Create auth tables first
            for sql in auth_tables:
                try:
                    cursor.execute(sql)
                    print("✅ Auth table created/verified")
                except Exception as e:
                    print(f"⚠️  Auth table warning: {e}")

            # Create user table
            for sql in user_tables:
                try:
                    cursor.execute(sql)
                    print("✅ User table created/verified")
                except Exception as e:
                    print(f"⚠️  User table warning: {e}")

            # Create many-to-many tables (CRITICAL for admin panel)
            for sql in m2m_tables:
                try:
                    cursor.execute(sql)
                    print("✅ Many-to-many table created/verified")
                except Exception as e:
                    print(f"⚠️  M2M table warning: {e}")

            # Create other tables
            for sql in other_tables:
                try:
                    cursor.execute(sql)
                    print("✅ Other table created/verified")
                except Exception as e:
                    print(f"⚠️  Other table warning: {e}")

        return True

    except Exception as e:
        print(f"❌ User table creation failed: {e}")
        return False

def run_django_migrations():
    """Run Django migrations to ensure everything is properly set up"""
    print("\n🔄 Running Django migrations...")
    
    try:
        from django.core.management import execute_from_command_line
        
        # Run migrations for each app
        apps_to_migrate = [
            'contenttypes',
            'auth', 
            'admin',
            'sessions',
            'sites',
            'users',
            'core',
            'challenges',
            'tutorials',
            'courses',
            'editor'
        ]
        
        for app in apps_to_migrate:
            try:
                print(f"   Migrating {app}...")
                execute_from_command_line(['manage.py', 'migrate', app, '--verbosity=0'])
                print(f"   ✅ {app} migrated")
            except Exception as e:
                print(f"   ⚠️  {app} migration warning: {str(e)[:50]}...")
        
        # Run all migrations
        print("   Running all migrations...")
        execute_from_command_line(['manage.py', 'migrate', '--verbosity=1'])
        print("✅ All migrations completed")
        
        return True
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        return False

def test_admin_functionality():
    """Test if admin functionality works"""
    print("\n🧪 Testing admin functionality...")
    
    try:
        from django.contrib.auth import get_user_model
        from django.contrib.auth.models import Group, Permission
        
        User = get_user_model()
        
        # Test user model
        user_count = User.objects.count()
        print(f"✅ User model works - {user_count} users")
        
        # Test groups
        group_count = Group.objects.count()
        print(f"✅ Group model works - {group_count} groups")
        
        # Test permissions
        permission_count = Permission.objects.count()
        print(f"✅ Permission model works - {permission_count} permissions")
        
        # Test if we can create a test user
        test_user, created = User.objects.get_or_create(
            username='test_admin',
            defaults={
                'email': '<EMAIL>',
                'is_staff': True,
                'is_superuser': True
            }
        )
        
        if created:
            test_user.set_password('test123')
            test_user.save()
            print("✅ Test admin user created")
        else:
            print("✅ Test admin user already exists")
        
        return True
        
    except Exception as e:
        print(f"❌ Admin functionality test failed: {e}")
        return False

def main():
    """Main function to fix all missing tables"""
    print("🚀 Comprehensive Database Table Fix")
    print("=" * 50)
    
    # Setup Django
    if not setup_django():
        return
    
    # Check database connection
    if not check_database_connection():
        return
    
    # Check missing tables
    missing_tables, existing_tables = get_missing_tables()
    
    if missing_tables:
        print(f"\n🔧 Found {len(missing_tables)} missing tables")
        
        # Create missing core tables
        if create_missing_core_tables():
            print("✅ Core tables created")
        
        # Create user model tables
        if create_user_model_tables():
            print("✅ User model tables created")
        
        # Run Django migrations
        if run_django_migrations():
            print("✅ Django migrations completed")
        
        # Test admin functionality
        if test_admin_functionality():
            print("✅ Admin functionality verified")
            
            print("\n🎉 SUCCESS! All database tables are now ready")
            print("\n📋 Next steps:")
            print("1. Restart your Python app in cPanel")
            print("2. Test admin panel: https://kodesql.in/admin/")
            print("3. Test user management in admin")
            print("4. Create a proper superuser if needed")
        else:
            print("\n⚠️  Tables created but admin has issues")
    else:
        print("\n✅ All required tables already exist")
        
        # Still test admin functionality
        if test_admin_functionality():
            print("✅ Admin functionality working")
        else:
            print("⚠️  Admin functionality has issues - running migrations...")
            run_django_migrations()

if __name__ == "__main__":
    main()
