# 🔧 KodeSQL cPanel Authentication Fix Guide

## 🚨 Problem Identified

Your authentication system was failing with 500 errors due to several configuration issues:

1. **CSRF Configuration**: Wrong trusted origins (ngrok URLs instead of production domain)
2. **Debug Middleware**: Production-unsafe debug middleware causing conflicts
3. **Static Files**: Incorrect paths for cPanel hosting environment
4. **Environment Variables**: Development settings instead of production values
5. **Database Configuration**: Missing cPanel-specific database settings

## ✅ Fixes Applied

### 1. CSRF Configuration Fixed
- Updated `CSRF_TRUSTED_ORIGINS` to include `kodesql.in` and `www.kodesql.in`
- Removed development ngrok URLs
- Fixed ALLOWED_HOSTS for production domain

### 2. Middleware Cleaned Up
- Removed `DebugAuthMiddleware` from production settings
- Streamlined middleware stack for better performance

### 3. Static Files Configuration
- Updated `STATIC_ROOT` to `/home/<USER>/public_html/static`
- Updated `MEDIA_ROOT` to `/home/<USER>/public_html/media`
- Added conditional paths for development vs production

### 4. Production Environment Variables
- Updated database hosts from `127.0.0.1` to `localhost`
- Fixed site URLs to use `https://kodesql.in`
- Updated CORS origins for production domain

### 5. Enhanced passenger_wsgi.py
- Added proper cPanel Python paths
- Implemented error handling for production
- Added fallback error page for debugging

### 6. Security Improvements
- Created production `.htaccess` with security headers
- Added file protection for sensitive files
- Configured static file serving

## 🚀 Deployment Instructions

### Step 1: Upload Files
Upload these updated files to your cPanel:
```
/home/<USER>/public_html/KodeSQL/
├── passenger_wsgi.py (updated)
├── .htaccess (new)
├── .env (updated)
├── sqlplayground/settings.py (updated)
└── fix_cpanel_auth_complete.py (new)
```

### Step 2: Run the Fix Script
In cPanel Terminal or SSH:
```bash
cd /home/<USER>/public_html/KodeSQL
python fix_cpanel_auth_complete.py
```

### Step 3: Restart Python Application
- Go to cPanel → Python App
- Click "Restart" for your KodeSQL application

### Step 4: Test Authentication
Visit these URLs to test:
- Login: `https://kodesql.in/auth/login/`
- Register: `https://kodesql.in/auth/register/`
- Admin: `https://kodesql.in/admin/`

## 🔍 Troubleshooting

### If you still get 500 errors:

1. **Check Error Logs**:
   ```bash
   tail -f /home/<USER>/logs/error.log
   ```

2. **Verify Database Connection**:
   ```bash
   python manage.py check --database=default
   ```

3. **Test Minimal WSGI**:
   - Backup: `mv passenger_wsgi.py passenger_wsgi_backup.py`
   - Use test WSGI from `debug_cpanel_error.py`

### Common Issues and Solutions:

**CSRF Token Errors**:
- Ensure `kodesql.in` is in `CSRF_TRUSTED_ORIGINS`
- Clear browser cache and cookies

**Database Connection Errors**:
- Verify database names in cPanel match `.env` file
- Check database user permissions

**Static Files Not Loading**:
- Run: `python manage.py collectstatic --noinput`
- Check file permissions: `chmod -R 755 /home/<USER>/public_html/static`

**Import Errors**:
- Verify virtual environment is activated
- Check Python paths in `passenger_wsgi.py`

## 📋 Post-Deployment Checklist

- [ ] Authentication pages load without 500 errors
- [ ] Login functionality works
- [ ] Registration functionality works
- [ ] Google OAuth works (if configured)
- [ ] Admin panel accessible
- [ ] Static files load correctly
- [ ] Email verification works
- [ ] Password reset works

## 🆘 Emergency Recovery

If something goes wrong:

1. **Restore Original Files**:
   ```bash
   mv passenger_wsgi_backup.py passenger_wsgi.py
   ```

2. **Use Minimal Settings**:
   - Copy `settings_minimal_auth.py` to `settings.py`

3. **Contact Support**:
   - Check cPanel error logs
   - Contact hosting provider if needed

## 🎯 Key Changes Summary

| Component | Before | After |
|-----------|--------|-------|
| CSRF Origins | ngrok URLs | kodesql.in |
| Debug Mode | Enabled | Disabled |
| Static Root | ./staticfiles | /home/<USER>/public_html/static |
| Database Host | 127.0.0.1 | localhost |
| Middleware | Debug included | Production-safe |
| Error Handling | Basic | Enhanced with fallback |

Your authentication system should now work correctly on cPanel hosting! 🎉
