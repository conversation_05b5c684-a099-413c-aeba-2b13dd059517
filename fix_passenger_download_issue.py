#!/usr/bin/env python3
"""
Fix passenger download issue and 404 errors
This script creates proper configurations for cPanel Django hosting
"""

import os
import sys

def create_simple_htaccess():
    """Create the simplest possible .htaccess"""
    print("🔧 Creating Simple .htaccess...")
    
    simple_htaccess = """# Simplest .htaccess for cPanel Django
# No complex rewrite rules to avoid download issues

PassengerEnabled on
PassengerAppRoot /home/<USER>/public_html/KodeSQL
PassengerPython /home/<USER>/virtualenv/public_html/KodeSQL/3.10/bin/python

# Protect sensitive files only
<Files ".env">
    Require all denied
</Files>

<Files "*.log">
    Require all denied
</Files>
"""
    
    try:
        with open('.htaccess_simple_fix', 'w') as f:
            f.write(simple_htaccess)
        print("✅ Created .htaccess_simple_fix")
        return True
    except Exception as e:
        print(f"❌ Failed to create simple .htaccess: {e}")
        return False

def create_no_htaccess_solution():
    """Create solution without .htaccess"""
    print("\n🔧 Creating No-.htaccess Solution...")
    
    # Create a simple index.html that redirects to Django
    index_html = """<!DOCTYPE html>
<html>
<head>
    <title>KodeSQL</title>
    <meta http-equiv="refresh" content="0; url=/passenger_wsgi.py">
</head>
<body>
    <p>Redirecting to KodeSQL...</p>
    <p>If not redirected, <a href="/passenger_wsgi.py">click here</a></p>
</body>
</html>"""
    
    try:
        with open('index.html', 'w') as f:
            f.write(index_html)
        print("✅ Created index.html redirect")
        return True
    except Exception as e:
        print(f"❌ Failed to create index.html: {e}")
        return False

def create_enhanced_passenger_wsgi():
    """Create enhanced passenger_wsgi.py with better error handling"""
    print("\n🔧 Creating Enhanced passenger_wsgi.py...")
    
    enhanced_wsgi = '''#!/usr/bin/env python3
"""
Enhanced WSGI configuration for cPanel hosting
Handles URL routing issues and provides better error reporting
"""
import os
import sys

# Add cPanel-specific paths
sys.path.insert(0, '/home/<USER>/public_html/KodeSQL')
sys.path.insert(0, '/home/<USER>/virtualenv/public_html/KodeSQL/3.10/lib/python3.10/site-packages')

# Set environment variables
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'sqlplayground.settings')
os.environ.setdefault('DEBUG', 'False')

def application(environ, start_response):
    """Enhanced WSGI application with better error handling"""
    
    try:
        # Import Django
        import django
        django.setup()
        
        from django.core.wsgi import get_wsgi_application
        django_app = get_wsgi_application()
        
        # Call Django application
        return django_app(environ, start_response)
        
    except Exception as e:
        # Enhanced error page
        status = '500 Internal Server Error'
        headers = [('Content-type', 'text/html; charset=utf-8')]
        start_response(status, headers)
        
        # Get request info
        request_uri = environ.get('REQUEST_URI', 'Unknown')
        request_method = environ.get('REQUEST_METHOD', 'Unknown')
        http_host = environ.get('HTTP_HOST', 'Unknown')
        
        error_html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>KodeSQL - Configuration Error</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 40px; }}
                .error {{ background: #ffebee; padding: 20px; border-radius: 5px; margin: 20px 0; }}
                .info {{ background: #f0f0f0; padding: 15px; margin: 10px 0; border-radius: 5px; }}
                .fix {{ background: #e8f5e8; padding: 15px; margin: 10px 0; border-radius: 5px; }}
            </style>
        </head>
        <body>
            <h1>🔧 KodeSQL Configuration Issue</h1>
            
            <div class="error">
                <h2>Error Details</h2>
                <p><strong>Request:</strong> {request_method} {request_uri}</p>
                <p><strong>Host:</strong> {http_host}</p>
                <p><strong>Error:</strong> {str(e)}</p>
            </div>
            
            <div class="info">
                <h2>Common Causes</h2>
                <ul>
                    <li>Django not properly installed in virtual environment</li>
                    <li>Database connection issues</li>
                    <li>Missing environment variables</li>
                    <li>Python path configuration problems</li>
                </ul>
            </div>
            
            <div class="fix">
                <h2>Quick Fixes</h2>
                <ol>
                    <li>Check cPanel Python App configuration</li>
                    <li>Restart Python application</li>
                    <li>Verify database credentials in .env</li>
                    <li>Run: python manage.py check</li>
                </ol>
            </div>
            
            <p><a href="/">← Try Again</a></p>
        </body>
        </html>
        """
        
        return [error_html.encode('utf-8')]

# For compatibility with older Passenger versions
if __name__ == "__main__":
    from wsgiref.simple_server import make_server
    httpd = make_server('', 8000, application)
    httpd.serve_forever()
'''
    
    try:
        with open('passenger_wsgi_enhanced.py', 'w') as f:
            f.write(enhanced_wsgi)
        os.chmod('passenger_wsgi_enhanced.py', 0o755)
        print("✅ Created passenger_wsgi_enhanced.py")
        return True
    except Exception as e:
        print(f"❌ Failed to create enhanced WSGI: {e}")
        return False

def create_cpanel_config_guide():
    """Create cPanel configuration guide"""
    print("\n🔧 Creating cPanel Configuration Guide...")
    
    guide = """# cPanel Python App Configuration Guide

## Problem: Passenger Download Issue & 404 Errors

When clicking links downloads passenger_wsgi.py instead of executing it, and some pages give 404 errors.

## Root Cause
- Complex .htaccess rewrite rules interfering with Passenger
- Incorrect cPanel Python App configuration
- URL routing conflicts

## Solution Steps

### Step 1: Simplify .htaccess
Replace your current .htaccess with the simplest version:

```bash
mv .htaccess .htaccess_backup
mv .htaccess_simple_fix .htaccess
```

### Step 2: Configure cPanel Python App Properly

1. Go to cPanel → Python App
2. Click on your KodeSQL application
3. Verify these settings:

**Application Root:** `/home/<USER>/public_html/KodeSQL`
**Application URL:** `/` (or leave empty)
**Application Startup File:** `passenger_wsgi.py`
**Python Version:** 3.10

### Step 3: Alternative - Use Enhanced WSGI
If simple .htaccess doesn't work:

```bash
mv passenger_wsgi.py passenger_wsgi_backup.py
mv passenger_wsgi_enhanced.py passenger_wsgi.py
```

### Step 4: Test URLs
After changes, test these URLs:
- https://kodesql.in/ (landing page)
- https://kodesql.in/dashboard/ (dashboard)
- https://kodesql.in/challenges/ (challenges)
- https://kodesql.in/auth/login/ (login)

### Step 5: If Still Not Working

#### Option A: Subdirectory Installation
Move your app to a subdirectory:
1. Create folder: `/home/<USER>/public_html/app/`
2. Move all files there
3. Update Python App settings
4. Access via: `https://kodesql.in/app/`

#### Option B: Contact Hosting Support
Ask your hosting provider to:
1. Verify Passenger configuration
2. Check Python app settings
3. Review error logs

## Common Issues & Solutions

| Issue | Solution |
|-------|----------|
| Downloads passenger_wsgi.py | Use simple .htaccess |
| 404 on all pages | Check Python App URL setting |
| 500 errors | Check error logs, verify database |
| Static files not loading | Run collectstatic |

## Testing Commands

```bash
# Test Django
python manage.py check

# Test database
python manage.py showmigrations

# Collect static files
python manage.py collectstatic --noinput

# Test URLs
python fix_passenger_download_issue.py
```

## Success Indicators
- ✅ No downloads when clicking links
- ✅ All navigation works
- ✅ Challenges page loads
- ✅ Login/register work
- ✅ Static files load properly
"""
    
    try:
        with open('CPANEL_CONFIG_GUIDE.md', 'w') as f:
            f.write(guide)
        print("✅ Created CPANEL_CONFIG_GUIDE.md")
        return True
    except Exception as e:
        print(f"❌ Failed to create guide: {e}")
        return False

def test_django_urls():
    """Test Django URL configuration"""
    print("\n🔧 Testing Django URLs...")
    
    try:
        # Setup Django
        sys.path.insert(0, os.getcwd())
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'sqlplayground.settings')
        
        import django
        django.setup()
        
        from django.urls import reverse
        
        # Test key URLs
        test_urls = [
            'core:landing_page',
            'core:dashboard', 
            'challenges:challenges_list',
            'users:login',
            'users:register',
        ]
        
        for url_name in test_urls:
            try:
                url = reverse(url_name)
                print(f"✅ {url_name}: {url}")
            except Exception as e:
                print(f"❌ {url_name}: {e}")
        
        return True
    except Exception as e:
        print(f"❌ Django URL test failed: {e}")
        return False

def main():
    """Main function to fix passenger download issue"""
    print("🚀 Fixing Passenger Download Issue & 404 Errors")
    print("=" * 50)
    
    fixes = [
        ("Simple .htaccess", create_simple_htaccess),
        ("Enhanced WSGI", create_enhanced_passenger_wsgi),
        ("Configuration Guide", create_cpanel_config_guide),
        ("Test Django URLs", test_django_urls),
    ]
    
    results = []
    for name, fix_func in fixes:
        print(f"\n--- {name} ---")
        try:
            result = fix_func()
            results.append((name, result))
        except Exception as e:
            print(f"❌ {name} failed: {e}")
            results.append((name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 FIX RESULTS:")
    
    for name, result in results:
        status = "✅" if result else "❌"
        print(f"{status} {name}")
    
    print("\n🔧 IMMEDIATE STEPS:")
    print("1. Replace .htaccess with simple version:")
    print("   mv .htaccess .htaccess_backup")
    print("   mv .htaccess_simple_fix .htaccess")
    print("")
    print("2. Restart Python app in cPanel")
    print("")
    print("3. Test: https://kodesql.in/challenges/")
    print("")
    print("4. If still downloading, check cPanel Python App settings")

if __name__ == "__main__":
    main()
