#!/usr/bin/env python3
"""
Fix Missing Table - challenges_userchallengeProgress
"""

import os
import sys
import django
from django.db import connection, transaction

def setup_django():
    """Setup Django environment"""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'sqlplayground.settings')
    django.setup()

def check_existing_tables():
    """Check what challenge progress tables exist"""
    print("🔍 Checking Existing Challenge Progress Tables...")
    
    try:
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT table_name FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name LIKE '%challenge%progress%'
            """)
            existing_tables = [row[0] for row in cursor.fetchall()]
            
            print(f"   Found tables: {existing_tables}")
            return existing_tables
            
    except Exception as e:
        print(f"   ❌ Error checking tables: {e}")
        return []

def create_user_challenge_progress_table():
    """Create the missing user challenge progress table"""
    print("🔧 Creating User Challenge Progress Table...")
    
    # Try different possible table names
    table_variations = [
        """
        CREATE TABLE IF NOT EXISTS challenges_userchallengeprogress (
            id SERIAL PRIMARY KEY,
            user_id INTEGER NOT NULL REFERENCES users_user(id) ON DELETE CASCADE,
            challenge_id INTEGER NOT NULL REFERENCES challenges_challenge(id) ON DELETE CASCADE,
            is_completed BOOLEAN NOT NULL DEFAULT FALSE,
            completed_at TIMESTAMP WITH TIME ZONE,
            attempts INTEGER NOT NULL DEFAULT 0 CHECK (attempts >= 0),
            best_query TEXT,
            xp_earned INTEGER NOT NULL DEFAULT 0 CHECK (xp_earned >= 0),
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
            UNIQUE(user_id, challenge_id)
        );
        """,
        """
        CREATE TABLE IF NOT EXISTS challenges_userchallengeProgress (
            id SERIAL PRIMARY KEY,
            user_id INTEGER NOT NULL REFERENCES users_user(id) ON DELETE CASCADE,
            challenge_id INTEGER NOT NULL REFERENCES challenges_challenge(id) ON DELETE CASCADE,
            is_completed BOOLEAN NOT NULL DEFAULT FALSE,
            completed_at TIMESTAMP WITH TIME ZONE,
            attempts INTEGER NOT NULL DEFAULT 0 CHECK (attempts >= 0),
            best_query TEXT,
            xp_earned INTEGER NOT NULL DEFAULT 0 CHECK (xp_earned >= 0),
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
            UNIQUE(user_id, challenge_id)
        );
        """
    ]
    
    try:
        with connection.cursor() as cursor:
            for i, table_sql in enumerate(table_variations, 1):
                try:
                    print(f"   Trying table variation {i}...")
                    cursor.execute(table_sql)
                    print(f"   ✅ Table variation {i} created successfully")
                    break
                except Exception as e:
                    print(f"   ⚠️  Table variation {i} failed: {e}")
                    continue
        
        # Create index
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS challenges_userchallengeprogress_user_id_idx 
            ON challenges_userchallengeprogress(user_id);
        """)
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS challenges_userchallengeprogress_challenge_id_idx 
            ON challenges_userchallengeprogress(challenge_id);
        """)
        
        print("   ✅ User Challenge Progress table created successfully")
        return True
        
    except Exception as e:
        print(f"   ❌ Error creating table: {e}")
        return False

def verify_table_creation():
    """Verify the table was created"""
    print("🔍 Verifying Table Creation...")
    
    try:
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT table_name FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND (table_name = 'challenges_userchallengeprogress' 
                     OR table_name = 'challenges_userchallengeProgress')
            """)
            tables = [row[0] for row in cursor.fetchall()]
            
            if tables:
                print(f"   ✅ Found table(s): {tables}")
                return True
            else:
                print("   ❌ Table still not found")
                return False
                
    except Exception as e:
        print(f"   ❌ Error verifying table: {e}")
        return False

def test_django_model():
    """Test if the Django model works now"""
    print("🔍 Testing Django Model...")
    
    try:
        from challenges.models import UserChallengeProgress
        
        # Test basic query
        count = UserChallengeProgress.objects.count()
        print(f"   ✅ UserChallengeProgress model working: {count} records")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Django model test failed: {e}")
        return False

def test_website_functionality():
    """Test if the website works now"""
    print("🔍 Testing Website Functionality...")
    
    try:
        from django.contrib.auth import get_user_model
        from challenges.models import Challenge
        from courses.models import Course
        
        User = get_user_model()
        
        # Test basic queries
        user_count = User.objects.count()
        challenge_count = Challenge.objects.count()
        course_count = Course.objects.count()
        
        print(f"   ✅ Users: {user_count}")
        print(f"   ✅ Challenges: {challenge_count}")
        print(f"   ✅ Courses: {course_count}")
        
        print("   ✅ All models working correctly")
        return True
        
    except Exception as e:
        print(f"   ❌ Website functionality test failed: {e}")
        return False

def main():
    """Main function"""
    print("🚀 Fix Missing User Challenge Progress Table")
    print("="*60)
    
    # Setup Django
    try:
        setup_django()
        print("✅ Django setup successful")
    except Exception as e:
        print(f"❌ Django setup failed: {e}")
        return
    
    # Run fixes
    steps = [
        ("Check Existing Tables", check_existing_tables),
        ("Create User Challenge Progress Table", create_user_challenge_progress_table),
        ("Verify Table Creation", verify_table_creation),
        ("Test Django Model", test_django_model),
        ("Test Website Functionality", test_website_functionality)
    ]
    
    failed_steps = []
    
    for step_name, step_func in steps:
        print(f"\n📋 {step_name}...")
        result = step_func()
        if step_name == "Check Existing Tables":
            continue  # This is just informational
        if not result:
            failed_steps.append(step_name)
    
    print("\n" + "="*60)
    print("📋 FIX RESULTS")
    print("="*60)
    
    if failed_steps:
        print(f"\n⚠️  Issues remaining: {len(failed_steps)}")
        for step in failed_steps:
            print(f"   - {step}")
    else:
        print("\n🎉 ALL ISSUES FIXED!")
    
    print("\n🚀 Next steps:")
    print("1. Test your domain: https://kodesql.in")
    print("2. The 500 error should now be resolved")
    print("3. All challenge functionality should work")
    print("4. Admin panel should be fully functional")

if __name__ == "__main__":
    main()
