# Fix CSRF Verification Failed - Signup Issue

## Problem
You're getting this error when trying to signup:
```
Forbidden (403)
CSRF verification failed. Request aborted.
Reason given for failure: CSRF token from POST incorrect.
```

## Root Cause
CSRF (Cross-Site Request Forgery) protection is failing because:
1. CSRF token is not being properly generated or transmitted
2. CSRF settings are not optimized for production
3. Browser cookies/session issues
4. JavaScript interference with form submission

## Solution Applied

### 1. Updated CSRF Settings
**File: `sqlplayground/settings.py`**

Fixed CSRF configuration for production:
```python
# CSRF Settings - Fixed for production
CSRF_COOKIE_HTTPONLY = False  # Must be False for JavaScript access
CSRF_COOKIE_SAMESITE = 'Lax'  # Allow same-site requests
CSRF_USE_SESSIONS = False  # Use cookies, not sessions for better compatibility
CSRF_COOKIE_SECURE = not DEBUG  # Secure in production, not in development
CSRF_FAILURE_VIEW = 'core.views.csrf_failure'  # Custom CSRF failure view
```

### 2. Added Custom CSRF Failure View
**File: `core/views.py`**

Created a helpful CSRF failure page that provides:
- Debug information
- Clear instructions for users
- Developer troubleshooting tips

### 3. Created CSRF Diagnostic Script
**File: `fix_csrf_issue.py`**

This script:
- Checks CSRF settings
- Tests CSRF token generation
- Verifies session configuration
- Creates test views for debugging

## Quick Fix Steps

### Step 1: Upload Updated Files
Upload these updated files to your cPanel:
- `sqlplayground/settings.py` (updated CSRF settings)
- `core/views.py` (added CSRF failure view)
- `fix_csrf_issue.py` (diagnostic script)

### Step 2: Run CSRF Diagnostic
```bash
cd /home/<USER>/public_html/KodeSQL
python fix_csrf_issue.py
```

### Step 3: Restart Python App
1. cPanel → Python App
2. Find KodeSQL application
3. Click "Restart"
4. Wait for "Running" status

### Step 4: Clear Browser Data
**Important**: Clear browser cookies and cache:
1. Open browser developer tools (F12)
2. Go to Application/Storage tab
3. Clear all cookies for kodesql.in
4. Clear cache
5. Refresh the page

### Step 5: Test Signup
1. Go to: https://kodesql.in/auth/register/
2. Fill out the signup form
3. Submit the form
4. Should work without CSRF errors

## Troubleshooting

### If CSRF Error Persists:

#### 1. Test CSRF Token Generation
Visit: https://kodesql.in/csrf-test/
- This will show if CSRF tokens are being generated
- Check browser console for JavaScript errors

#### 2. Check Browser Console
1. Open developer tools (F12)
2. Go to Console tab
3. Look for CSRF-related errors
4. Check if CSRF token is present in form

#### 3. Verify Form Template
The signup form should have:
```html
<form method="post">
    {% csrf_token %}
    <!-- form fields -->
</form>
```

#### 4. Check Network Tab
1. Open developer tools (F12)
2. Go to Network tab
3. Submit the form
4. Check if `csrfmiddlewaretoken` is in the POST data

### Common Fixes:

#### Browser Issues:
- **Clear all cookies** for kodesql.in
- **Disable browser extensions** temporarily
- **Try incognito/private mode**
- **Try different browser**

#### JavaScript Issues:
- Check if JavaScript is modifying the form
- Ensure no AJAX is interfering with form submission
- Verify no JavaScript errors in console

#### Server Issues:
- Restart Python application
- Check cPanel error logs
- Verify CSRF middleware is enabled

## Verification Steps

### 1. Check CSRF Settings
```bash
python -c "
import os, django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'sqlplayground.settings')
django.setup()
from django.conf import settings
print('CSRF_COOKIE_SECURE:', settings.CSRF_COOKIE_SECURE)
print('CSRF_COOKIE_SAMESITE:', settings.CSRF_COOKIE_SAMESITE)
print('CSRF_USE_SESSIONS:', settings.CSRF_USE_SESSIONS)
print('CSRF_TRUSTED_ORIGINS:', settings.CSRF_TRUSTED_ORIGINS)
"
```

### 2. Test CSRF Token
```bash
python -c "
import os, django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'sqlplayground.settings')
django.setup()
from django.test import RequestFactory
from django.middleware.csrf import get_token
from django.contrib.sessions.middleware import SessionMiddleware

factory = RequestFactory()
request = factory.get('/')
middleware = SessionMiddleware(lambda req: None)
middleware.process_request(request)
request.session.save()
token = get_token(request)
print('CSRF Token Generated:', len(token) > 10)
print('Token Length:', len(token))
"
```

### 3. Test Signup Form
1. Visit: https://kodesql.in/auth/register/
2. Right-click → View Page Source
3. Search for `csrfmiddlewaretoken`
4. Should find a hidden input with a long token value

## Success Indicators

After applying the fix:
- ✅ Signup form submits without CSRF errors
- ✅ CSRF test page works: https://kodesql.in/csrf-test/
- ✅ Browser console shows no CSRF errors
- ✅ Form contains valid CSRF token
- ✅ User registration completes successfully

## Prevention

To prevent CSRF issues in the future:
1. **Always include `{% csrf_token %}`** in POST forms
2. **Don't modify CSRF settings** without understanding implications
3. **Test forms after deployment** changes
4. **Keep CSRF_TRUSTED_ORIGINS** updated with your domains
5. **Monitor browser console** for JavaScript errors

## Additional Resources

### Test URLs:
- **CSRF Test**: https://kodesql.in/csrf-test/
- **Signup Form**: https://kodesql.in/auth/register/
- **Debug Info**: https://kodesql.in/debug-info/

### Log Files:
- **cPanel Error Logs**: Check for CSRF-related errors
- **Browser Console**: Check for JavaScript errors
- **Network Tab**: Verify CSRF token in POST requests

The fix ensures CSRF protection works correctly while maintaining security for your Django application on cPanel hosting.
